<variant
    name="debug"
    package="com.autolink.sbjk"
    minSdkVersion="30"
    targetSdkVersion="34"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.1.4"
    partialResultsDir="build\intermediates\lint_partial_results\debug\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\23194ec92608fd675ebd4e8fcd4953e4\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\test\AndroidManifest.xml"
        javaDirectories="src\test\java;src\testDebug\java;src\test\kotlin;src\testDebug\kotlin"
        assetsDirectories="src\test\assets;src\testDebug\assets"
        unitTest="true"/>
    <sourceProvider
        manifests="src\androidTest\AndroidManifest.xml"
        javaDirectories="src\androidTest\java;src\androidTestDebug\java;src\androidTest\kotlin;src\androidTestDebug\kotlin"
        resDirectories="src\androidTest\res;src\androidTestDebug\res"
        assetsDirectories="src\androidTest\assets;src\androidTestDebug\assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <mainArtifact
      classOutputs="build\intermediates\javac\debug\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\debug\R.jar"
      applicationId="com.autolink.sbjk"
      generatedSourceFolders="build\generated\ap_generated_sources\debug\out;build\generated\aidl_source_output_dir\debug\out"
      generatedResourceFolders="build\generated\res\resValues\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\23194ec92608fd675ebd4e8fcd4953e4\transformed\D8BackportedDesugaredMethods.txt">
  </mainArtifact>
  <androidTestArtifact
      applicationId="com.autolink.sbjk.test"
      generatedResourceFolders="build\generated\res\resValues\androidTest\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\23194ec92608fd675ebd4e8fcd4953e4\transformed\D8BackportedDesugaredMethods.txt">
  </androidTestArtifact>
  <testArtifact>
  </testArtifact>
</variant>
