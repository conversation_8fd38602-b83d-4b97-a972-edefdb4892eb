1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.autolink.sbjk"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="1"
6    android:versionName="1.0" >
7
8    <uses-sdk
8-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:8:5-10:40
9        android:minSdkVersion="30"
9-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:9:9-35
10        android:targetSdkVersion="34" />
10-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:10:9-38
11
12    <!-- 相机权限 -->
13    <uses-permission android:name="android.permission.CAMERA" />
13-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:13:5-65
13-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:13:22-62
14    <uses-permission android:name="android.permission.SYSTEM_CAMERA" />
14-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:14:5-72
14-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:14:22-69
15    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
15-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:15:5-79
15-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:15:22-77
16
17    <!-- 存储相关权限 -->
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:18:5-81
18-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:18:22-78
19    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
19-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:19:5-82
19-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:19:22-79
20
21    <!-- 音频权限 -->
22    <uses-permission android:name="android.permission.RECORD_AUDIO" />
22-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:22:5-71
22-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:22:22-68
23
24    <!-- 系统权限 -->
25    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
25-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:25:5-77
25-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:25:22-75
26    <uses-permission android:name="android.permission.START_ACTIVITIES_FROM_BACKGROUND" />
26-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:26:5-90
26-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:26:22-88
27    <uses-permission android:name="android.car.permission.CAR_POWER" />
27-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:27:5-71
27-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:27:22-69
28    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
28-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:28:5-80
28-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:28:22-78
29
30    <!-- 车辆服务权限 -->
31    <uses-permission android:name="android.permission.BIND_SERVICE" />
31-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:31:5-70
31-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:31:22-68
32    <uses-permission android:name="android.car.permission.CAR_INFO" />
32-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:32:5-70
32-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:32:22-68
33    <uses-permission android:name="android.car.permission.CAR_ENGINE_DETAILED" />
33-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:33:5-81
33-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:33:22-79
34
35    <!-- 系统级权限（用于车辆服务registerCallback接口） -->
36    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
36-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:36:5-84
36-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:36:22-82
37    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
37-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:15:5-79
37-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:15:22-77
38
39    <!-- 摄像头硬件特性 -->
40    <uses-feature
40-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:40:5-42:34
41        android:glEsVersion="0x20000"
41-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:41:9-38
42        android:required="true" />
42-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:42:9-32
43    <uses-feature
43-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:43:5-45:35
44        android:name="android.hardware.camera"
44-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:44:9-47
45        android:required="false" />
45-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:45:9-33
46    <uses-feature
46-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:46:5-48:35
47        android:name="android.hardware.camera.autofocus"
47-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:47:9-57
48        android:required="false" />
48-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:48:9-33
49    <uses-feature
49-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:49:5-51:35
50        android:name="android.hardware.camera.front"
50-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:50:9-53
51        android:required="false" />
51-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:51:9-33
52    <uses-feature
52-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:52:5-54:35
53        android:name="android.hardware.microphone"
53-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:53:9-51
54        android:required="false" />
54-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:54:9-33
55
56    <queries>
56-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:56:5-60:15
57        <intent>
57-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:57:9-59:18
58            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
58-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:58:13-85
58-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:58:21-83
59        </intent>
60    </queries>
61
62    <permission
62-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:62:5-64:46
63        android:name="com.autolink.sbjk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
63-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:63:9-82
64        android:protectionLevel="signature" />
64-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:64:9-44
65
66    <uses-permission android:name="com.autolink.sbjk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
66-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:65:5-97
66-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:65:22-95
67
68    <application
68-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:67:5-157:19
69        android:allowBackup="true"
69-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:68:9-35
70        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
70-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
71        android:dataExtractionRules="@xml/data_extraction_rules"
71-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:69:9-65
72        android:debuggable="true"
73        android:extractNativeLibs="false"
73-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:77:9-42
74        android:fullBackupContent="@xml/backup_rules"
74-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:70:9-54
75        android:hardwareAccelerated="true"
75-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:79:9-43
76        android:icon="@mipmap/ic_launcher"
76-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:71:9-43
77        android:label="@string/app_name"
77-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:72:9-41
78        android:persistent="true"
78-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:76:9-34
79        android:roundIcon="@mipmap/ic_launcher_round"
79-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:73:9-54
80        android:supportsRtl="true"
80-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:74:9-35
81        android:theme="@style/Theme.Sbjk" >
81-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:75:9-42
82        <activity
82-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:81:9-89:20
83            android:name="com.autolink.sbjk.MainActivity"
83-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:82:13-41
84            android:configChanges="uiMode|orientation|screenSize|screenLayout|keyboardHidden"
84-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:84:13-94
85            android:exported="true" >
85-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:83:13-36
86            <intent-filter>
86-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:85:13-88:29
87                <action android:name="android.intent.action.MAIN" />
87-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:86:17-69
87-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:86:25-66
88
89                <category android:name="android.intent.category.LAUNCHER" />
89-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:87:17-77
89-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:87:27-74
90            </intent-filter>
91        </activity>
92        <activity
92-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:91:9-93:46
93            android:name="com.autolink.dvr.p003ui.file.FileActivity"
93-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:92:13-69
94            android:launchMode="singleTask" />
94-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:93:13-44
95        <activity
95-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:95:9-97:46
96            android:name="com.autolink.dvr.p003ui.VideoActivity"
96-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:96:13-65
97            android:launchMode="singleTask" />
97-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:97:13-44
98        <activity
98-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:99:9-102:72
99            android:name="com.autolink.sbjk.CameraPreviewActivity"
99-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:100:13-50
100            android:screenOrientation="landscape"
100-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:101:13-50
101            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
101-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:102:13-69
102
103        <service
103-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:104:9-112:19
104            android:name="com.autolink.sbjk.common.service.AidlService"
104-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:105:13-55
105            android:enabled="true"
105-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:106:13-35
106            android:exported="true" >
106-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:107:13-36
107            <intent-filter>
107-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:108:13-111:29
108                <action android:name="com.autolink.sbjk.aidl.service" />
108-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:109:17-72
108-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:109:25-70
109
110                <category android:name="android.intent.category.DEFAULT" />
110-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:110:17-75
110-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:110:27-73
111            </intent-filter>
112        </service>
113        <service android:name="com.autolink.sbjk.common.service.DVRService" />
113-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:114:9-115:56
113-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:115:13-54
114
115        <receiver
115-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:117:9-124:20
116            android:name="com.autolink.sbjk.common.receiver.BootCompleteReceiver"
116-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:118:13-65
117            android:enabled="true"
117-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:119:13-35
118            android:exported="true" >
118-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:120:13-36
119            <intent-filter>
119-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:121:13-123:29
120                <action android:name="android.intent.action.BOOT_COMPLETED" />
120-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:122:17-78
120-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:122:25-76
121            </intent-filter>
122        </receiver>
123
124        <uses-library
124-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:126:9-128:39
125            android:name="androidx.camera.extensions.impl"
125-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:127:13-59
126            android:required="false" />
126-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:128:13-37
127
128        <service
128-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:130:9-138:19
129            android:name="androidx.camera.core.impl.MetadataHolderService"
129-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:131:13-75
130            android:enabled="false"
130-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:132:13-36
131            android:exported="false" >
131-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:133:13-37
132            <meta-data
132-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:135:13-137:88
133                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
133-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:136:17-103
134                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
134-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:137:17-86
135        </service>
136
137        <provider
137-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:140:9-150:20
138            android:name="androidx.startup.InitializationProvider"
138-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:141:13-67
139            android:authorities="com.autolink.sbjk.androidx-startup"
139-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:143:13-69
140            android:exported="false" >
140-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:142:13-37
141            <meta-data
141-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:144:13-146:51
142                android:name="androidx.emoji2.text.EmojiCompatInitializer"
142-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:145:17-75
143                android:value="androidx.startup" />
143-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:146:17-49
144            <meta-data
144-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:147:13-149:51
145                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
145-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:148:17-78
146                android:value="androidx.startup" />
146-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:149:17-49
147            <meta-data
147-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
148                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
148-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
149                android:value="androidx.startup" />
149-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
150        </provider>
151
152        <service
152-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:152:9-156:54
153            android:name="com.autolink.sbjk.service.CameraService"
153-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:153:13-50
154            android:enabled="true"
154-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:154:13-35
155            android:exported="false"
155-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:155:13-37
156            android:foregroundServiceType="camera" />
156-->E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:156:13-51
157
158        <receiver
158-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
159            android:name="androidx.profileinstaller.ProfileInstallReceiver"
159-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
160            android:directBootAware="false"
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
161            android:enabled="true"
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
162            android:exported="true"
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
163            android:permission="android.permission.DUMP" >
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
164            <intent-filter>
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
165                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
165-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
165-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
166            </intent-filter>
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
168                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
168-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
168-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
171                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
171-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
171-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
172            </intent-filter>
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
174                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
175            </intent-filter>
176        </receiver>
177    </application>
178
179</manifest>
