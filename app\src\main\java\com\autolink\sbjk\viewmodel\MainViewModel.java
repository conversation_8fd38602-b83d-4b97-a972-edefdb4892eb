package com.autolink.sbjk.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.autolink.sbjk.common.base.BaseViewModel;
import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.model.entity.CameraInfo;
import com.autolink.sbjk.model.repository.CameraRepository;
import com.autolink.sbjk.model.repository.RecordingRepository;
import com.autolink.sbjk.model.state.CameraState;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import com.autolink.sbjk.common.util.LogUtil;

import java.util.Map;

/**
 * 主界面ViewModel
 * 管理主界面的业务逻辑和状态
 */
public class MainViewModel extends BaseViewModel {
    
    private static final String TAG = "MainViewModel";
    
    private final CameraRepository cameraRepository;
    private final RecordingRepository recordingRepository;
    
    // UI状态
    private final MutableLiveData<Boolean> _isAllCamerasRecording = new MutableLiveData<>(false);
    public final LiveData<Boolean> isAllCamerasRecording = _isAllCamerasRecording;
    
    private final MutableLiveData<String> _recordingDuration = new MutableLiveData<>("00:00:00");
    public final LiveData<String> recordingDuration = _recordingDuration;
    
    private final MutableLiveData<Boolean> _isSystemReady = new MutableLiveData<>(false);
    public final LiveData<Boolean> isSystemReady = _isSystemReady;
    
    // 相机状态聚合
    private final MediatorLiveData<CameraState> _aggregatedCameraState = new MediatorLiveData<>();
    public final LiveData<CameraState> aggregatedCameraState = _aggregatedCameraState;
    
    // 录制开始时间
    private long recordingStartTime = 0;
    private final android.os.Handler uiHandler = new android.os.Handler(android.os.Looper.getMainLooper());
    private Runnable durationUpdateRunnable;

    // Handler回调管理
    private final Set<Runnable> activeCallbacks = ConcurrentHashMap.newKeySet();
    
    // 主构造函数 - 支持依赖注入
    public MainViewModel(@NonNull CameraRepository cameraRepository,
                        @NonNull RecordingRepository recordingRepository) {
        super();

        this.cameraRepository = cameraRepository;
        this.recordingRepository = recordingRepository;

        setupObservers();
        initializeDurationUpdater();

        LogUtil.d(TAG, "MainViewModel initialized with injected dependencies");
    }

    // 兼容性构造函数 - 用于现有代码
    public MainViewModel(@NonNull Application application) {
        this(CameraRepository.getInstance(application),
             RecordingRepository.getInstance(application));

        LogUtil.d(TAG, "MainViewModel initialized with application context");
    }
    
    /**
     * 设置观察者
     */
    private void setupObservers() {
        // 观察相机状态变化
        _aggregatedCameraState.addSource(cameraRepository.cameraState, cameraState -> {
            if (cameraState != null) {
                _aggregatedCameraState.setValue(cameraState);
                _isAllCamerasRecording.setValue(cameraState.isAllCamerasRecording());
                _isSystemReady.setValue(cameraState.isSystemReady());
                
                // 检查是否有错误
                if (cameraState.hasAnyError()) {
                    setError(cameraState.getAllErrors());
                }
                
                LogUtil.d(TAG, "Camera state updated: " + cameraState.toString());
            }
        });
    }
    
    /**
     * 初始化录制时长更新器
     */
    private void initializeDurationUpdater() {
        durationUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isCleared()) {
                    updateRecordingDuration();
                    safePostDelayedCallback(this, 1000); // 每秒更新一次
                }
            }
        };
    }
    
    /**
     * 开始所有相机录制
     */
    public void startAllCamerasRecording() {
        if (getCurrentLoadingState()) {
            LogUtil.w(TAG, "Already in loading state, ignoring start request");
            return;
        }
        
        setLoading(true);
        executeAsync(() -> {
            try {
                LogUtil.i(TAG, "Starting all cameras recording");
                
                String basePath = CameraConstants.DEFAULT_RECORD_PATH;
                cameraRepository.startAllRecording(basePath);
                
                // 开始录制时间计算
                recordingStartTime = System.currentTimeMillis();
                safePostCallback(durationUpdateRunnable);
                
                setSuccess("开始录制所有摄像头");
                
            } catch (Exception e) {
                setError("启动录制失败", e);
            }
        });
    }
    
    /**
     * 停止所有相机录制
     */
    public void stopAllCamerasRecording() {
        if (getCurrentLoadingState()) {
            LogUtil.w(TAG, "Already in loading state, ignoring stop request");
            return;
        }
        
        setLoading(true);
        executeAsync(() -> {
            try {
                LogUtil.i(TAG, "Stopping all cameras recording");
                
                cameraRepository.stopAllRecording();
                
                // 停止录制时间计算
                recordingStartTime = 0;
                uiHandler.removeCallbacks(durationUpdateRunnable);
                _recordingDuration.postValue("00:00:00");
                
                setSuccess("停止录制所有摄像头");
                
            } catch (Exception e) {
                setError("停止录制失败", e);
            }
        });
    }
    
    /**
     * 切换录制状态
     */
    public void toggleRecording() {
        Boolean isRecording = _isAllCamerasRecording.getValue();
        if (isRecording != null && isRecording) {
            stopAllCamerasRecording();
        } else {
            startAllCamerasRecording();
        }
    }
    
    /**
     * 开始单个相机录制
     */
    public void startCameraRecording(String cameraId) {
        if (getCurrentLoadingState()) {
            return;
        }
        
        setLoading(true);
        executeAsync(() -> {
            try {
                String outputPath = CameraConstants.DEFAULT_RECORD_PATH + "/" + 
                                  CameraConstants.getCameraName(cameraId);
                cameraRepository.startRecording(cameraId, outputPath);
                setSuccess(CameraConstants.getCameraName(cameraId) + "开始录制");
            } catch (Exception e) {
                setError("启动" + CameraConstants.getCameraName(cameraId) + "录制失败", e);
            }
        });
    }
    
    /**
     * 停止单个相机录制
     */
    public void stopCameraRecording(String cameraId) {
        if (getCurrentLoadingState()) {
            return;
        }
        
        setLoading(true);
        executeAsync(() -> {
            try {
                cameraRepository.stopRecording(cameraId);
                setSuccess(CameraConstants.getCameraName(cameraId) + "停止录制");
            } catch (Exception e) {
                setError("停止" + CameraConstants.getCameraName(cameraId) + "录制失败", e);
            }
        });
    }
    
    /**
     * 获取相机信息
     */
    public CameraInfo getCameraInfo(String cameraId) {
        return cameraRepository.getCameraInfo(cameraId);
    }
    
    /**
     * 获取所有相机信息
     */
    public Map<String, CameraInfo> getAllCameraInfo() {
        return cameraRepository.getAllCameraInfo();
    }
    
    /**
     * 清除相机错误
     */
    public void clearCameraError(String cameraId) {
        cameraRepository.clearCameraError(cameraId);
    }
    
    /**
     * 清除所有错误
     */
    public void clearAllErrors() {
        clearError();
        CameraState currentState = _aggregatedCameraState.getValue();
        if (currentState != null) {
            currentState.clearAllErrors();
        }
    }
    
    /**
     * 更新录制时长显示
     */
    private void updateRecordingDuration() {
        if (recordingStartTime > 0) {
            long duration = System.currentTimeMillis() - recordingStartTime;
            String formattedDuration = formatDuration(duration);
            _recordingDuration.postValue(formattedDuration);
        }
    }
    
    /**
     * 格式化时长显示
     */
    private String formatDuration(long durationMs) {
        long seconds = durationMs / 1000;
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        seconds = seconds % 60;
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
    
    /**
     * 预览Surface管理方法
     */
    public void onPreviewSurfaceAvailable(String cameraId, android.view.Surface surface) {
        safeExecute(() -> {
            LogUtil.d(TAG, "Preview surface available for camera " + cameraId);

            // 验证Surface有效性
            if (surface != null && surface.isValid()) {
                cameraRepository.setPreviewSurface(cameraId, surface);
            } else {
                LogUtil.w(TAG, "Invalid surface provided for camera " + cameraId);
            }
        });
    }

    public void onPreviewSurfaceChanged(String cameraId, android.view.Surface surface) {
        safeExecute(() -> {
            LogUtil.d(TAG, "Preview surface changed for camera " + cameraId);

            // 验证Surface有效性
            if (surface != null && surface.isValid()) {
                cameraRepository.updatePreviewSurface(cameraId, surface);
            } else {
                LogUtil.w(TAG, "Invalid surface provided for camera " + cameraId);
            }
        });
    }

    public void onPreviewSurfaceDestroyed(String cameraId) {
        safeExecute(() -> {
            LogUtil.d(TAG, "Preview surface destroyed for camera " + cameraId);
            cameraRepository.clearPreviewSurface(cameraId);
        });
    }

    /**
     * 暂停所有预览但保持录制
     */
    public void pauseAllPreviews() {
        safeExecute(() -> {
            LogUtil.d(TAG, "Pausing all camera previews");
            cameraRepository.pauseAllPreviews();
        });
    }

    /**
     * 恢复所有预览
     */
    public void resumeAllPreviews() {
        safeExecute(() -> {
            LogUtil.d(TAG, "Resuming all camera previews");
            cameraRepository.resumeAllPreviews();
        });
    }

    /**
     * 检查系统是否准备就绪
     */
    public boolean isSystemHealthy() {
        CameraState state = _aggregatedCameraState.getValue();
        return state != null && state.isHealthy();
    }

    /**
     * 安全地发送Handler回调
     */
    private void safePostCallback(Runnable callback) {
        if (!isCleared() && uiHandler != null) {
            activeCallbacks.add(callback);
            uiHandler.post(() -> {
                try {
                    if (!isCleared()) {
                        callback.run();
                    }
                } finally {
                    activeCallbacks.remove(callback);
                }
            });
        }
    }

    /**
     * 安全地发送延迟Handler回调
     */
    private void safePostDelayedCallback(Runnable callback, long delayMillis) {
        if (!isCleared() && uiHandler != null) {
            activeCallbacks.add(callback);
            uiHandler.postDelayed(() -> {
                try {
                    if (!isCleared()) {
                        callback.run();
                    }
                } finally {
                    activeCallbacks.remove(callback);
                }
            }, delayMillis);
        }
    }

    @Override
    protected void onViewModelCleared() {
        super.onViewModelCleared();

        // 清理所有Handler回调
        if (uiHandler != null) {
            // 移除所有回调和消息
            uiHandler.removeCallbacksAndMessages(null);

            // 清理活跃回调集合
            for (Runnable callback : activeCallbacks) {
                uiHandler.removeCallbacks(callback);
            }
            activeCallbacks.clear();
        }

        LogUtil.d(TAG, "MainViewModel cleared with all callbacks removed");
    }
}
