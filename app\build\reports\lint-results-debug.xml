<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.4">

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="            cameraManager.openCamera(cameraId, new CameraDevice.StateCallback() {"
        errorLine2="            ^">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\encoder\CameraYuvEncoder.java"
            line="544"
            column="13"/>
    </issue>

    <issue
        id="MissingSuperCall"
        severity="Error"
        message="Overriding method should call `super.onBackPressed`"
        category="Correctness"
        priority="9"
        summary="Missing Super Call"
        explanation="Some methods, such as `View#onDetachedFromWindow`, require that you also call the super implementation as part of your method."
        errorLine1="    public void onBackPressed() {"
        errorLine2="                ~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="845"
            column="17"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.autolink.dvr.p003ui.file.FileActivity`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;com.autolink.dvr.p003ui.file.FileActivity&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="92"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.autolink.dvr.p003ui.VideoActivity`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;com.autolink.dvr.p003ui.VideoActivity&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="96"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.autolink.sbjk.common.service.AidlService`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;.common.service.AidlService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="105"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.autolink.sbjk.common.service.DVRService`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;.common.service.DVRService&quot;/>"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="115"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.autolink.sbjk.common.receiver.BootCompleteReceiver`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;.common.receiver.BootCompleteReceiver&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="118"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `androidx.startup.InitializationProvider`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;androidx.startup.InitializationProvider&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="141"
            column="27"/>
    </issue>

    <issue
        id="ScopedStorage"
        severity="Warning"
        message="The Google Play store has a policy that limits usage of MANAGE_EXTERNAL_STORAGE"
        category="Correctness"
        priority="8"
        summary="Affected by scoped storage"
        explanation="Scoped storage is enforced on Android 10+ (or Android 11+ if using `requestLegacyExternalStorage`). In particular, `WRITE_EXTERNAL_STORAGE` will no longer provide write access to all files; it will provide the equivalent of `READ_EXTERNAL_STORAGE` instead.&#xA;&#xA;As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:&#xA;* `android.permission.READ_MEDIA_IMAGES`&#xA;* `android.permission.READ_MEDIA_VIDEO`&#xA;* `android.permission.READ_MEDIA_AUDIO`&#xA;&#xA;and then add `maxSdkVersion=&quot;33&quot;` to the older permission. See the developer guide for how to do this: https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions&#xA;&#xA;The `MANAGE_EXTERNAL_STORAGE` permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at https://goo.gle/android-mediastore-createwriterequest.&#xA;&#xA;To learn more, read these resources: Play policy: https://goo.gle/policy-storage-help Allowable use cases: https://goo.gle/policy-storage-usecases"
        url="https://goo.gle/android-storage-usecases"
        urls="https://goo.gle/android-storage-usecases"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.MANAGE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="19"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\viewmodel\CameraPreviewViewModel.java"
            line="244"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return String.format(&quot;%02d:%02d&quot;, minutes, seconds);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="1793"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\viewmodel\MainViewModel.java"
            line="276"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                String lowerName = name.toLowerCase();"
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\scanner\QuickVideoScanner.java"
            line="132"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                String lowerName = name.toLowerCase();"
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\scanner\QuickVideoScanner.java"
            line="241"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\widget\RecordingIndicator.java"
            line="218"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%02d:%02d&quot;, minutes, seconds);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\widget\RecordingIndicator.java"
            line="220"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            monthDisplayValues[i] = String.format(&quot;%d&quot;, i);"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java"
            line="145"
            column="37"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            hourDisplayValues[i] = String.format(&quot;%02d&quot;, i - 1);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java"
            line="221"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            dayDisplayValues[i] = String.format(&quot;%d&quot;, i);"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java"
            line="322"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                btnHourPicker.setText(String.format(&quot;%02d时&quot;, selectedHour));"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java"
            line="494"
            column="39"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return String.format(&quot;%d月%d日%d时&quot;, selectedMonth, selectedDay, selectedHour);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java"
            line="576"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%.1f KB&quot;, fileSize / 1024.0);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\VideoRecordInfo.java"
            line="127"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%.1f MB&quot;, fileSize / (1024.0 * 1024.0));"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\VideoRecordInfo.java"
            line="129"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%.1f GB&quot;, fileSize / (1024.0 * 1024.0 * 1024.0));"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\VideoRecordInfo.java"
            line="131"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%.1f KB&quot;, fileSize / 1024.0);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java"
            line="148"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%.1f MB&quot;, fileSize / (1024.0 * 1024.0));"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java"
            line="150"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%.1f GB&quot;, fileSize / (1024.0 * 1024.0 * 1024.0));"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java"
            line="152"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java"
            line="166"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;%02d:%02d&quot;, minutes, seconds);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java"
            line="168"
            column="20"/>
    </issue>

    <issue
        id="DiscouragedPrivateApi"
        severity="Warning"
        message="Reflective access to mMediaPlayer, which is not part of the public SDK and therefore likely to change in future Android releases"
        category="Correctness"
        priority="6"
        summary="Using Discouraged Private API"
        explanation="Usage of restricted non-SDK interface may throw an exception at runtime. Accessing non-SDK methods or fields through reflection has a high likelihood to break your app between versions, and is being restricted to facilitate future app compatibility."
        url="https://developer.android.com/preview/restrictions-non-sdk-interfaces"
        urls="https://developer.android.com/preview/restrictions-non-sdk-interfaces"
        errorLine1="                java.lang.reflect.Field field = VideoView.class.getDeclaredField(&quot;mMediaPlayer&quot;);"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\PlaybackSpeedManager.java"
            line="115"
            column="49"/>
    </issue>

    <issue
        id="NotificationPermission"
        severity="Error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission"
        category="Correctness"
        priority="6"
        summary="Notifications Without Permission"
        explanation="When targeting Android 13 and higher, posting permissions requires holding the runtime permission `android.permission.POST_NOTIFICATIONS`."
        errorLine1="            manager.notify(NOTIFICATION_ID, notification);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\service\CameraService.java"
            line="251"
            column="13"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        android:targetSdkVersion=&quot;33&quot;/>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.SYSTEM_CAMERA&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="14"
            column="22"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.INTERACT_ACROSS_USERS&quot;/>"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="15"
            column="22"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.START_ACTIVITIES_FROM_BACKGROUND&quot;/>"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="26"
            column="22"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.INTERACT_ACROSS_USERS_FULL&quot;/>"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="36"
            column="22"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.INTERACT_ACROSS_USERS&quot;/>"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="37"
            column="22"/>
    </issue>

    <issue
        id="UseSwitchCompatOrMaterialCode"
        severity="Warning"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library"
        category="Correctness"
        priority="5"
        summary="Replace usage of `Switch` widget"
        explanation="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library"
        errorLine1="    private Switch switchSentryAuto;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="94"
            column="5"/>
    </issue>

    <issue
        id="UseSwitchCompatOrMaterialXml"
        severity="Warning"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library"
        category="Correctness"
        priority="5"
        summary="Replace usage of `Switch` widget"
        explanation="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library"
        errorLine1="                    &lt;Switch"
        errorLine2="                    ^">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="103"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.appcompat:appcompat:1.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="38"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.camera:camera-core than 1.3.2 is available: 1.4.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.camera:camera-core:1.3.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="44"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.camera:camera-camera2 than 1.3.2 is available: 1.4.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.camera:camera-camera2:1.3.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="45"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.camera:camera-lifecycle than 1.3.2 is available: 1.4.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.camera:camera-lifecycle:1.3.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="46"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.camera:camera-view than 1.3.2 is available: 1.4.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.camera:camera-view:1.3.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.2.1 is available: 1.3.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.2.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="54"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.6.1 is available: 3.7.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.6.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\build.gradle"
            line="55"
            column="31"/>
    </issue>

    <issue
        id="GradleOverrides"
        severity="Warning"
        message="This `minSdkVersion` value (`23`) is not used; it is always overridden by the value specified in the Gradle build script (`30`)"
        category="Correctness"
        priority="4"
        summary="Value overridden by Gradle build script"
        explanation="The value of (for example) `minSdkVersion` is only used if it is not specified in the `build.gradle` build scripts. When specified in the Gradle build scripts, the manifest value is ignored and can be misleading, so should be removed to avoid ambiguity."
        errorLine1="        android:minSdkVersion=&quot;23&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="9"
            column="9"/>
    </issue>

    <issue
        id="GradleOverrides"
        severity="Warning"
        message="This `targetSdkVersion` value (`33`) is not used; it is always overridden by the value specified in the Gradle build script (`34`)"
        category="Correctness"
        priority="4"
        summary="Value overridden by Gradle build script"
        explanation="The value of (for example) `minSdkVersion` is only used if it is not specified in the `build.gradle` build scripts. When specified in the Gradle build scripts, the manifest value is ignored and can be misleading, so should be removed to avoid ambiguity."
        errorLine1="        android:targetSdkVersion=&quot;33&quot;/>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="Deprecated"
        severity="Warning"
        message="Consider removing `sharedUserId` for new users by adding `android:sharedUserMaxSdkVersion=&quot;32&quot;` to your manifest. See https://developer.android.com/guide/topics/manifest/manifest-element for details."
        category="Correctness"
        priority="2"
        summary="Using deprecated resources"
        explanation="Deprecated views, attributes and so on are deprecated because there is a better way to do something. Do it that new way. You&apos;ve been warned."
        errorLine1="    android:sharedUserId=&quot;android.uid.system&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="4"
            column="5"/>
    </issue>

    <issue
        id="ExportedService"
        severity="Warning"
        message="Exported service does not require permission"
        category="Security"
        priority="5"
        summary="Exported service does not require permission"
        explanation="Exported services (services which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service."
        url="https://goo.gle/ExportedService"
        urls="https://goo.gle/ExportedService"
        errorLine1="        &lt;service"
        errorLine2="         ~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml"
            line="104"
            column="10"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="                    adapter.notifyDataSetChanged();"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="1260"
            column="21"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="                    adapter.notifyDataSetChanged();"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="1328"
            column="21"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="        notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\adapter\VideoListAdapter.java"
            line="71"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="        notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\adapter\VideoListAdapter.java"
            line="81"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\PlaybackSpeedManager.java"
            line="113"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\PlaybackSpeedManager.java"
            line="205"
            column="16"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        &lt;item name=&quot;android:windowLightStatusBar&quot; tools:targetApi=&quot;m&quot;>true&lt;/item>"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\values\themes.xml"
            line="17"
            column="51"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields (static reference to `PlaybackLifecycleManager` which has field `hostActivity` pointing to `Activity`); this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private static volatile PlaybackLifecycleManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\lifecycle\PlaybackLifecycleManager.java"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields (static reference to `VehicleManager` which has field `mContext` pointing to `Context`); this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private static VehicleManager sInstance;"
        errorLine2="            ~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\vehicle\VehicleManager.java"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="        android:pathData=&quot;M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z&quot;/>"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\drawable\settings_icon.xml"
            line="9"
            column="27"/>
    </issue>

    <issue
        id="DisableBaselineAlignment"
        severity="Warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance"
        category="Performance"
        priority="3"
        summary="Missing `baselineAligned` attribute"
        explanation="When a `LinearLayout` is used to distribute the space proportionally between nested layouts, the baseline alignment property should be turned off to make the layout computation faster."
        errorLine1="                    &lt;LinearLayout"
        errorLine2="                     ~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="213"
            column="22"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/background_primary_adaptive` with a theme that also paints a background (inferred theme is `@style/Theme.Sbjk`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/background_primary_adaptive&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.Sbjk`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;?android:attr/selectableItemBackground&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\item_video_record.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        &lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\drawable\play_pause_button_state.xml"
            line="4"
            column="17"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        &lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\drawable\play_pause_button_state.xml"
            line="15"
            column="17"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://material.io/components/dialogs/"
        urls="https://material.io/components/dialogs/"
        errorLine1="                        &lt;Button"
        errorLine2="                         ~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="150"
            column="26"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://material.io/components/dialogs/"
        urls="https://material.io/components/dialogs/"
        errorLine1="                        &lt;Button"
        errorLine2="                         ~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="162"
            column="26"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://material.io/components/dialogs/"
        urls="https://material.io/components/dialogs/"
        errorLine1="                        &lt;Button"
        errorLine2="                         ~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="175"
            column="26"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://material.io/components/dialogs/"
        urls="https://material.io/components/dialogs/"
        errorLine1="                        &lt;Button"
        errorLine2="                         ~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="187"
            column="26"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://material.io/components/dialogs/"
        urls="https://material.io/components/dialogs/"
        errorLine1="                        &lt;Button"
        errorLine2="                         ~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="199"
            column="26"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="Custom view ``Button`` has `setOnTouchListener` called on it but does not override `performClick`"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        btnAllCameras.setOnTouchListener((v, event) -> {"
        errorLine2="        ^">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="352"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        btnAllCameras.setOnTouchListener((v, event) -> {"
        errorLine2="                                         ^">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="352"
            column="42"/>
    </issue>

    <issue
        id="ConstantLocale"
        severity="Warning"
        message="Assigning `Locale.getDefault()` to a final static field is suspicious; this code will not work correctly if the user changes locale while the app is running"
        category="Internationalization"
        priority="6"
        summary="Constant Locale"
        explanation="Assigning `Locale.getDefault()` to a constant is suspicious, because the locale can change while the app is running."
        errorLine1="    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat(&quot;yyyyMMdd_HHmmss&quot;, Locale.getDefault());"
        errorLine2="                                                                                                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\encoder\CameraYuvEncoder.java"
            line="167"
            column="97"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvCameraInfo.setText(&quot;相机: &quot; + cameraName);"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\CameraPreviewActivity.java"
            line="212"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                        tvCurrentTime.setText(&quot;00:00&quot;);"
        errorLine2="                                              ~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java"
            line="1894"
            column="47"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            btnMonthPicker.setText(selectedMonth + &quot;月&quot;);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java"
            line="479"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                btnDayPicker.setText(selectedDay + &quot;日&quot;);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java"
            line="486"
            column="38"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;开始录制&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;开始录制&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_camera_preview.xml"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;哨兵监控&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;哨兵监控&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="42"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;录像回放&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;录像回放&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="58"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;自动启动哨兵功能&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;自动启动哨兵功能&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="97"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;全部&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;全部&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="156"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;前视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;前视&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="169"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;后视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;后视&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="182"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;左视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;左视&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="194"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;右视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;右视&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="205"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;全部&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;全部&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="232"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;月份&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;月份&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="251"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;日期&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;日期&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="270"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;小时&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;小时&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="289"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;2024年12月19日 14:30:25&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;2024年12月19日 14:30:25&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="356"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;前视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;前视&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="403"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;后视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;后视&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="443"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;左视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;左视&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="483"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;右视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;右视&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="523"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;▶&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;▶&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="593"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;1.0x&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;1.0x&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="614"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;00:00&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml"
            line="628"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;前视&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;前视&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\item_video_record.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;2024年12月19日 14:30:25&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;2024年12月19日 14:30:25&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\XM\rsbjk-a\app\src\main\res\layout\item_video_record.xml"
            line="30"
            column="9"/>
    </issue>

</issues>
