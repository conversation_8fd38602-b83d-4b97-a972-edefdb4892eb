/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.autolink.app.vehicleservice;
public interface IVehicleControl extends android.os.IInterface
{
  /** Default implementation for IVehicleControl. */
  public static class Default implements com.autolink.app.vehicleservice.IVehicleControl
  {
    @Override public void registerCallback(com.autolink.app.vehicleservice.IVehicleControlCallback callback, int[] propertyKeys) throws android.os.RemoteException
    {
    }
    @Override public void unregisterCallback(com.autolink.app.vehicleservice.IVehicleControlCallback callback) throws android.os.RemoteException
    {
    }
    @Override public void setInt(int propKey, int value) throws android.os.RemoteException
    {
    }
    @Override public void setFloat(int propKey, float value) throws android.os.RemoteException
    {
    }
    @Override public int getInt(int propKey) throws android.os.RemoteException
    {
      return 0;
    }
    @Override public float getFloat(int propKey) throws android.os.RemoteException
    {
      return 0.0f;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.autolink.app.vehicleservice.IVehicleControl
  {
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.autolink.app.vehicleservice.IVehicleControl interface,
     * generating a proxy if needed.
     */
    public static com.autolink.app.vehicleservice.IVehicleControl asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.autolink.app.vehicleservice.IVehicleControl))) {
        return ((com.autolink.app.vehicleservice.IVehicleControl)iin);
      }
      return new com.autolink.app.vehicleservice.IVehicleControl.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
      }
      switch (code)
      {
        case TRANSACTION_registerCallback:
        {
          com.autolink.app.vehicleservice.IVehicleControlCallback _arg0;
          _arg0 = com.autolink.app.vehicleservice.IVehicleControlCallback.Stub.asInterface(data.readStrongBinder());
          int[] _arg1;
          _arg1 = data.createIntArray();
          this.registerCallback(_arg0, _arg1);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_unregisterCallback:
        {
          com.autolink.app.vehicleservice.IVehicleControlCallback _arg0;
          _arg0 = com.autolink.app.vehicleservice.IVehicleControlCallback.Stub.asInterface(data.readStrongBinder());
          this.unregisterCallback(_arg0);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_setInt:
        {
          int _arg0;
          _arg0 = data.readInt();
          int _arg1;
          _arg1 = data.readInt();
          this.setInt(_arg0, _arg1);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_setFloat:
        {
          int _arg0;
          _arg0 = data.readInt();
          float _arg1;
          _arg1 = data.readFloat();
          this.setFloat(_arg0, _arg1);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_getInt:
        {
          int _arg0;
          _arg0 = data.readInt();
          int _result = this.getInt(_arg0);
          reply.writeNoException();
          reply.writeInt(_result);
          break;
        }
        case TRANSACTION_getFloat:
        {
          int _arg0;
          _arg0 = data.readInt();
          float _result = this.getFloat(_arg0);
          reply.writeNoException();
          reply.writeFloat(_result);
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements com.autolink.app.vehicleservice.IVehicleControl
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void registerCallback(com.autolink.app.vehicleservice.IVehicleControlCallback callback, int[] propertyKeys) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeStrongInterface(callback);
          _data.writeIntArray(propertyKeys);
          boolean _status = mRemote.transact(Stub.TRANSACTION_registerCallback, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void unregisterCallback(com.autolink.app.vehicleservice.IVehicleControlCallback callback) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeStrongInterface(callback);
          boolean _status = mRemote.transact(Stub.TRANSACTION_unregisterCallback, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void setInt(int propKey, int value) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          _data.writeInt(value);
          boolean _status = mRemote.transact(Stub.TRANSACTION_setInt, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void setFloat(int propKey, float value) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          _data.writeFloat(value);
          boolean _status = mRemote.transact(Stub.TRANSACTION_setFloat, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public int getInt(int propKey) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        int _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getInt, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readInt();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public float getFloat(int propKey) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        float _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getFloat, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readFloat();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
    }
    static final int TRANSACTION_registerCallback = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_unregisterCallback = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_setInt = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_setFloat = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    static final int TRANSACTION_getInt = (android.os.IBinder.FIRST_CALL_TRANSACTION + 4);
    static final int TRANSACTION_getFloat = (android.os.IBinder.FIRST_CALL_TRANSACTION + 5);
  }
  public static final java.lang.String DESCRIPTOR = "com.autolink.app.vehicleservice.IVehicleControl";
  public void registerCallback(com.autolink.app.vehicleservice.IVehicleControlCallback callback, int[] propertyKeys) throws android.os.RemoteException;
  public void unregisterCallback(com.autolink.app.vehicleservice.IVehicleControlCallback callback) throws android.os.RemoteException;
  public void setInt(int propKey, int value) throws android.os.RemoteException;
  public void setFloat(int propKey, float value) throws android.os.RemoteException;
  public int getInt(int propKey) throws android.os.RemoteException;
  public float getFloat(int propKey) throws android.os.RemoteException;
}
