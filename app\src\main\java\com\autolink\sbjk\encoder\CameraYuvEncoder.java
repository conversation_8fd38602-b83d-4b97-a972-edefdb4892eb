package com.autolink.sbjk.encoder;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.ImageFormat;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CaptureRequest;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.Image;
import android.media.ImageReader;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;
import com.autolink.sbjk.common.util.LogUtil;
import android.util.Size;
import android.view.Surface;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.io.File;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.ArrayDeque;
import java.util.Arrays;
import java.util.Date;
import java.util.Locale;
import java.util.Queue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

public class CameraYuvEncoder {
    private static final String TAG = "CameraYuvEncoder";

    // 编码器默认配置
    private static final String DEFAULT_CODEC_TYPE = "H.264";
    private static final int FIXED_WIDTH = 1280;
    private static final int FIXED_HEIGHT = 800;
    private static final int DEFAULT_BITRATE = 1000000; // 1Mbps
    private static final int DEFAULT_FRAME_RATE = 15;
    private static final int DEFAULT_I_FRAME_INTERVAL = 2;

    // 图像处理相关
    private static final int MAX_IMAGES = 4; // 减少缓冲区大小，防止图像堆积和内存占用过高
    private boolean isProcessingFrame = false; // 防止多线程处理同一帧
    private int frameSkipCounter = 0;  // 用于跳帧计数
    private static final int FRAME_SKIP_THRESHOLD = 3;  // 每隔几帧处理一次（减轻CPU负担）
    private volatile boolean isStopping = false; // 停止录像标志位，防止停止过程中继续处理帧

    private Context context;
    private Context applicationContext; // 使用 Application Context 避免 Activity 泄漏
    private String cameraId;
    private String codecType;
    private int bitRateValue;
    private int cameraFormat = ImageFormat.YUV_420_888; // 默认摄像头格式

    private CameraDevice cameraDevice;
    private CameraCaptureSession captureSession;
    private ImageReader imageReader;
    private HandlerThread backgroundThread;
    private Handler backgroundHandler;
    private MediaCodec mediaCodec;
    private MediaMuxer mediaMuxer;
    private int videoTrackIndex = -1;
    private boolean muxerStarted = false;
    private File outputFile;        // 录制时的临时文件(.tmp)
    private File finalOutputFile;   // 录制完成后的最终文件(.mp4)
    private long startTime = 0;
    private final Semaphore cameraOpenCloseLock = new Semaphore(1);

    private String recordPath; // 录制保存路径
    
    // 分段录制相关变量
    private static final long DEFAULT_SEGMENT_DURATION_MS = 10 * 60 * 1000; // 默认10分钟一个片段
    private long segmentDurationMs = DEFAULT_SEGMENT_DURATION_MS; // 分段时长，可修改
    private long segmentStartTimeMs = 0; // 当前片段开始时间
    private int segmentCount = 1; // 片段计数
    private String baseFileName; // 基础文件名（不含序号）
    private boolean segmentRecording = true; // 是否启用分段录制
    private long lastSegmentCheckTimeMs = 0; // 上次检查分段时间
    private static final long SEGMENT_CHECK_INTERVAL_MS = 15000; // 每5秒检查一次分段

    private int width = FIXED_WIDTH;
    private int height = FIXED_HEIGHT;

    // 记录相机实际输出尺寸
    private int actualWidth;
    private int actualHeight;

    private Surface previewSurface; // 用于预览的Surface

    // 🚀 预分配固定大小缓存数组 - 消除动态分配
    private static final int FIXED_Y_SIZE = FIXED_WIDTH * FIXED_HEIGHT;           // 1,024,000
    private static final int FIXED_UV_SIZE = FIXED_Y_SIZE / 2;                    // 512,000
    private static final int FIXED_TOTAL_SIZE = FIXED_Y_SIZE + FIXED_UV_SIZE;     // 1,536,000
    private static final int JPEG_BUFFER_SIZE = FIXED_TOTAL_SIZE + 512000;        // 2,048,000

    // 核心数据缓存 - 三重缓冲避免竞争
    private byte[] primaryDataCache;      // 主缓存
    private byte[] secondaryDataCache;    // 备用缓存
    private byte[] previewDataCache;      // 预览缓存

    // 分量处理缓存
    private byte[] yPlaneCache;           // Y分量专用
    private byte[] uvPlaneCache;          // UV分量专用

    // 格式转换缓存
    private byte[] nv21ConversionCache;   // NV12→NV21转换
    private byte[] jpegCompressionCache;  // JPEG压缩

    // 缓存使用状态标记
    private volatile boolean primaryCacheInUse = false;
    private volatile boolean secondaryCacheInUse = false;
    private volatile boolean previewCacheInUse = false;

    // 直接预览相关变量（替代解码器预览）
    private boolean enableDirectPreview = false; // 是否启用直接预览
    private boolean useDirectPreview = true; // 使用直接预览模式（向后兼容开关）

    // 预览渲染相关
    private HandlerThread previewThread;
    private Handler previewHandler;
    private ByteBuffer nv12SharedBuffer; // 共享的NV12数据缓冲区
    private final Object bufferLock = new Object(); // 缓冲区同步锁
    private ImageReader previewImageReader; // 用于预览的ImageReader

    // 实际图像尺寸（用于预览渲染）
    private int actualImageWidth = FIXED_WIDTH;
    private int actualImageHeight = FIXED_HEIGHT;

    // 缓冲区池化管理
    private static final int BUFFER_POOL_SIZE = 6;
    private final Queue<ByteBuffer> bufferPool = new ArrayDeque<>(BUFFER_POOL_SIZE);

    // 添加用于标记需要在下一个关键帧处切换片段的变量
    private boolean switchSegmentOnNextKeyFrame = false;

    // 🚀 缓存Bitmap优化 - 避免重复创建Bitmap
    private Bitmap cachedPreviewBitmap = null;
    private Matrix cachedScaleMatrix = null;
    private Paint cachedBitmapPaint = null;

    // 🚀 对象复用优化 - 减少GC压力 (统一使用unifiedDataCache)
    // 移除重复的缓存数组，统一使用unifiedDataCache
    private java.io.ByteArrayOutputStream cachedOutputStream = null;
    private Rect cachedCompressRect = null;
    private BitmapFactory.Options cachedBitmapOptions = null;

    // 静态缓存SimpleDateFormat实例
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());

    // 静态 Handler 避免内存泄漏
    private static class SafeHandler extends Handler {
        private final WeakReference<Context> contextRef;

        SafeHandler(Context context) {
            super(Looper.getMainLooper());
            this.contextRef = new WeakReference<>(context.getApplicationContext());
        }

        void postToast(String message, int duration) {
            post(() -> {
                Context context = contextRef.get();
                if (context != null) {
                    Toast.makeText(context, message, duration).show();
                }
            });
        }
    }

    private SafeHandler safeHandler;

    public CameraYuvEncoder(Context context) {
        this.context = context;
        // 使用 Application Context 避免 Activity 泄漏
        this.applicationContext = context.getApplicationContext();
        // 初始化安全的 Handler
        this.safeHandler = new SafeHandler(context);
        // 🚀 预分配基础缓存，避免运行时分配
        preallocateBasicCaches();
    }

    /**
     * 🚀 预分配基础缓存数组
     * 在构造函数中调用，确保核心缓存始终可用
     */
    private void preallocateBasicCaches() {
        primaryDataCache = new byte[FIXED_TOTAL_SIZE];
        secondaryDataCache = new byte[FIXED_TOTAL_SIZE];
        previewDataCache = new byte[FIXED_TOTAL_SIZE];
        LogUtil.i(TAG, "基础缓存预分配完成: 3 × " + FIXED_TOTAL_SIZE + " 字节 = " +
                  (FIXED_TOTAL_SIZE * 3 / 1024 / 1024) + "MB");
    }

    /**
     * 🚀 预分配所有缓存数组
     * 在startTest()中调用，分配处理和转换相关缓存
     */
    private void preallocateAllCaches() {
        if (yPlaneCache == null) {
            yPlaneCache = new byte[FIXED_Y_SIZE];
            uvPlaneCache = new byte[FIXED_UV_SIZE];
            nv21ConversionCache = new byte[FIXED_TOTAL_SIZE];
            jpegCompressionCache = new byte[JPEG_BUFFER_SIZE];
            LogUtil.i(TAG, "完整缓存预分配完成: 总计约" +
                      ((FIXED_TOTAL_SIZE * 3 + FIXED_Y_SIZE + FIXED_UV_SIZE + FIXED_TOTAL_SIZE + JPEG_BUFFER_SIZE) / 1024 / 1024) + "MB");
        }
    }

    /**
     * 🚀 智能获取可用数据缓存
     * 优先使用主缓存，其次备用缓存，最后预览缓存
     */
    private byte[] getAvailableDataCache() {
        if (!primaryCacheInUse) {
            primaryCacheInUse = true;
            return primaryDataCache;
        } else if (!secondaryCacheInUse) {
            secondaryCacheInUse = true;
            return secondaryDataCache;
        } else {
            // 降级到预览缓存，可以随时覆盖
            previewCacheInUse = true;
            return previewDataCache;
        }
    }

    /**
     * 🚀 释放数据缓存
     * 标记缓存为可用状态
     */
    private void releaseDataCache(byte[] cache) {
        if (cache == primaryDataCache) {
            primaryCacheInUse = false;
        } else if (cache == secondaryDataCache) {
            secondaryCacheInUse = false;
        } else if (cache == previewDataCache) {
            previewCacheInUse = false;
        }
        // 其他缓存不需要释放标记
    }

    public void setCameraFormat(int format) {
        // 允许设置摄像头输出格式
        this.cameraFormat = format;
    }

    public void startTest(String cameraId) {
        this.cameraId = cameraId;
        this.codecType = DEFAULT_CODEC_TYPE;
        this.bitRateValue = DEFAULT_BITRATE;

        // 重置停止标志位，准备开始新的录制
        isStopping = false;
        isProcessingFrame = false;
        frameSkipCounter = 0;

        // 🚀 预分配所有缓存
        preallocateAllCaches();

        startBackgroundThread();
        
        try {
            // 首先设置MediaMuxer
            setupMediaMuxer();

            // 总是初始化预览线程，以备后用
            initPreviewThread();
            LogUtil.i(TAG, "预览线程已初始化");

            // 如果已经设置了预览Surface，启用预览
            if (previewSurface != null && useDirectPreview) {
                enableDirectPreview = true;
                LogUtil.i(TAG, "预览Surface已存在，启用直接预览");
            }

            // 打开相机，获取实际的输出尺寸
            openCamera();
            
            // 在相机初始化之后再设置MediaCodec，这样可以使用实际的相机输出尺寸
            Handler mainHandler = new Handler(Looper.getMainLooper());
            mainHandler.postDelayed(() -> {
                try {
                    setupMediaCodec();
                    LogUtil.i(TAG, "相机初始化完成后设置MediaCodec，使用实际尺寸: " + actualWidth + "x" + actualHeight);
                } catch (IOException e) {
                    LogUtil.e(TAG, "设置MediaCodec失败", e);
                    Toast.makeText(context, "设置编码器失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    cleanup();
                }
            }, 500); // 延迟500ms，确保相机已完全初始化

        } catch (Exception e) {
            LogUtil.e(TAG, "启动相机失败", e);
            // 使用安全的 Handler 显示Toast
            final String errorMessage = "启动相机失败: " + e.getMessage();
            if (safeHandler != null) {
                safeHandler.postToast(errorMessage, Toast.LENGTH_SHORT);
            }
            cleanup();
        }
    }

    private void setupMediaCodec() throws IOException {
        // 使用固定分辨率
        final int encoderWidth = FIXED_WIDTH;
        final int encoderHeight = FIXED_HEIGHT;
        
        final String mimeType = codecType.equals("H.264") ? MediaFormat.MIMETYPE_VIDEO_AVC : MediaFormat.MIMETYPE_VIDEO_HEVC;
        
        final MediaFormat format = MediaFormat.createVideoFormat(mimeType, encoderWidth, encoderHeight);
        
        // 明确指定NV12格式（大多数Android设备的相机实际输出格式），而不是使用灵活格式
        // COLOR_FormatYUV420SemiPlanar 对应 NV12
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420SemiPlanar);
        
        format.setInteger(MediaFormat.KEY_BIT_RATE, bitRateValue);
        format.setInteger(MediaFormat.KEY_FRAME_RATE, DEFAULT_FRAME_RATE);
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, DEFAULT_I_FRAME_INTERVAL);
        
        // 使用低延迟模式
        //format.setInteger(MediaFormat.KEY_PRIORITY, 0);  // 设置为最高优先级
        
        // 设置复杂度，允许编码器使用更少的CPU资源，牺牲一些质量
        //format.setInteger(MediaFormat.KEY_COMPLEXITY, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR);
        
        // 对于摄像头录制，使用CBR（固定码率）更适合
        format.setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR);
        
        // 直接设置缓冲区大小 - 2MB（减小缓冲区大小）
        final int bufferSize = 2 * 1024 * 1024;
        
        format.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, bufferSize);
        
        LogUtil.i(TAG, "设置编码器分辨率: " + encoderWidth + "x" + encoderHeight +
              "，使用NV12格式(COLOR_FormatYUV420SemiPlanar)");

        mediaCodec = MediaCodec.createEncoderByType(mimeType);
        mediaCodec.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        mediaCodec.start();
    }

    private void setupMediaMuxer() throws IOException {
        final String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        File directory;
        
        if (recordPath != null && !recordPath.isEmpty()) {
            directory = new File(recordPath);
        } else {
            directory = new File(context.getExternalFilesDir(null), "VideoTests");
        }
        
        if (!directory.exists()) {
            if (!directory.mkdirs()) {
                LogUtil.w(TAG, "创建目录失败: " + directory.getAbsolutePath());
            }
        }
        
        // 防止codecType为空
        if (codecType == null) {
            codecType = DEFAULT_CODEC_TYPE;
        }
        
        final String extension = codecType.equals("H.264") ? ".mp4" : ".mkv";
        
        // 根据摄像头ID获取对应的方向名称
        final String cameraDirection;
        switch (cameraId) {
            case "26":
                cameraDirection = "前视";
                break;
            case "30":
                cameraDirection = "后视";
                break;
            case "34":
                cameraDirection = "左视";
                break;
            case "38":
                cameraDirection = "右视";
                break;
            default:
                cameraDirection = "摄像头" + cameraId;
        }
        
        // 创建文件名（去掉片段序号）
        baseFileName = cameraDirection + "_" + timestamp;

        // 录制时使用.tmp后缀，完成后重命名为最终格式
        final String tempFileName = baseFileName + ".tmp";
        final String finalFileName = baseFileName + extension;

        outputFile = new File(directory, tempFileName);
        finalOutputFile = new File(directory, finalFileName);
        
        mediaMuxer = new MediaMuxer(outputFile.getAbsolutePath(), MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
        segmentStartTimeMs = System.currentTimeMillis(); // 记录片段开始时间
    }

    private void startBackgroundThread() {
        backgroundThread = new HandlerThread("CameraBackground");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
    }

    /**
     * 初始化预览线程
     */
    private void initPreviewThread() {
        if (previewThread == null) {
            previewThread = new HandlerThread("DirectPreviewThread");
            previewThread.start();
            previewHandler = new Handler(previewThread.getLooper());

            // 初始化缓冲区池
            initBufferPool();

            // LogUtil.d(TAG, "预览线程已初始化");
        }
    }

    /**
     * 初始化缓冲区池
     */
    private void initBufferPool() {
        synchronized (bufferLock) {
            bufferPool.clear();

            // 计算NV12缓冲区大小：Y平面 + UV平面
            final int ySize = FIXED_WIDTH * FIXED_HEIGHT;
            final int uvSize = ySize / 2;
            final int totalSize = ySize + uvSize;

            // 预分配缓冲区池
            for (int i = 0; i < BUFFER_POOL_SIZE; i++) {
                ByteBuffer buffer = ByteBuffer.allocateDirect(totalSize);
                bufferPool.offer(buffer);
            }

            // LogUtil.d(TAG, "缓冲区池已初始化，大小: " + BUFFER_POOL_SIZE + ", 每个缓冲区: " + totalSize + " 字节");
        }
    }

    private void openCamera() {
        CameraManager cameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
        try {
            if (!cameraOpenCloseLock.tryAcquire(2500, TimeUnit.MILLISECONDS)) {
                throw new RuntimeException("获取相机锁超时");
            }
            
            // 获取相机硬件支持的分辨率
            CameraCharacteristics characteristics = cameraManager.getCameraCharacteristics(cameraId);
            StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
            if (map == null) {
                throw new RuntimeException("无法获取相机流配置");
            }
            
            // 使用固定分辨率1280x800
            actualWidth = FIXED_WIDTH;
            actualHeight = FIXED_HEIGHT;
            
            // 使用摄像头固定尺寸创建图像读取器
            imageReader = ImageReader.newInstance(
                    FIXED_WIDTH, 
                    FIXED_HEIGHT, 
                    cameraFormat, MAX_IMAGES);
                    
            imageReader.setOnImageAvailableListener(
                reader -> {
                    Image image = null;

                    try {
                        // 检查是否正在停止录像
                        if (isStopping) {
                            // 获取并关闭图像，避免缓冲区堵塞
                            image = reader.acquireLatestImage();
                            if (image != null) {
                                image.close();
                            }
                            return;
                        }

                        // 跳帧处理，减轻CPU负担
                        if (frameSkipCounter < FRAME_SKIP_THRESHOLD) {
                            frameSkipCounter++;
                            // 获取并关闭图像，避免缓冲区堵塞
                            image = reader.acquireLatestImage();
                            if (image != null) {
                                image.close();
                            }
                            return;
                        }

                        // 重置跳帧计数
                        frameSkipCounter = 0;

                        // 如果已经在处理帧，跳过此帧
                        if (isProcessingFrame) {
                            // 获取并关闭图像，避免缓冲区堵塞
                            image = reader.acquireLatestImage();
                            if (image != null) {
                                image.close();
                            }
                            return;
                        }

                        isProcessingFrame = true;
                        image = reader.acquireLatestImage();

                        // 如果没有新图像，跳过
                        if (image == null) {
                            isProcessingFrame = false;
                            return;
                        }

                        encodeImageToVideo(image);
                    } finally {
                        if (image != null) {
                            image.close();
                        }
                        isProcessingFrame = false;
                    }
                },
                backgroundHandler
            );

            // 打开相机
            cameraManager.openCamera(cameraId, new CameraDevice.StateCallback() {
                @Override
                public void onOpened(@NonNull CameraDevice camera) {
                    cameraOpenCloseLock.release();
                    cameraDevice = camera;
                    createCaptureSession();
                }

                @Override
                public void onDisconnected(@NonNull CameraDevice camera) {
                    cameraOpenCloseLock.release();
                    camera.close();
                    cameraDevice = null;
                }

                @Override
                public void onError(@NonNull CameraDevice camera, int error) {
                    cameraOpenCloseLock.release();
                    camera.close();
                    cameraDevice = null;
                    // 使用安全的 Handler 显示Toast
                    if (safeHandler != null) {
                        safeHandler.postToast("相机打开失败: 错误码 " + error, Toast.LENGTH_SHORT);
                    }
                }
            }, backgroundHandler);
        } catch (CameraAccessException e) {
            LogUtil.e(TAG, "相机访问异常", e);
            // 使用安全的 Handler 显示Toast
            final String errorMessage = "相机访问异常: " + e.getMessage();
            if (safeHandler != null) {
                safeHandler.postToast(errorMessage, Toast.LENGTH_SHORT);
            }
        } catch (InterruptedException e) {
            LogUtil.e(TAG, "获取相机锁被中断", e);
            // 使用安全的 Handler 显示Toast
            if (safeHandler != null) {
                safeHandler.postToast("获取相机锁被中断", Toast.LENGTH_SHORT);
            }
        }
    }

    /**
     * 设置预览Surface，用于显示相机数据
     * @param surface 来自SurfaceView或TextureView的Surface
     */
    public void setPreviewSurface(Surface surface) {
        this.previewSurface = surface;
        if (surface != null && surface.isValid()) {
            enableDirectPreview = true;

            // 确保预览线程已初始化
            if (previewThread == null) {
                initPreviewThread();
            }

            LogUtil.i(TAG, "预览Surface已设置，启用直接预览模式");
        } else {
            enableDirectPreview = false;
            if (surface != null) {
                LogUtil.w(TAG, "预览Surface无效，禁用直接预览");
            } else {
                LogUtil.i(TAG, "预览Surface已清除，禁用直接预览");
            }
        }
    }

    /**
     * 设置预览模式
     * @param direct true=使用直接预览，false=使用传统解码预览（已废弃）
     */
    public void setPreviewMode(boolean direct) {
        this.useDirectPreview = direct;
        LogUtil.i(TAG, "预览模式设置为: " + (direct ? "直接预览" : "解码预览（已废弃）"));
    }
    


    private void createCaptureSession() {
        try {
            // 创建Surface列表
            final Surface encoderSurface = imageReader.getSurface();
            
            // 准备surfaces列表，仅添加编码器surface
            final java.util.ArrayList<Surface> surfaces = new java.util.ArrayList<>(1);
            surfaces.add(encoderSurface);
            
            // 创建预览请求
            final CaptureRequest.Builder captureRequestBuilder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            captureRequestBuilder.addTarget(encoderSurface);
            
            // 创建捕获会话 - 不再将预览Surface添加为目标
            cameraDevice.createCaptureSession(surfaces, new CameraCaptureSession.StateCallback() {
                @Override
                public void onConfigured(@NonNull CameraCaptureSession session) {
                    if (cameraDevice == null) {
                        return;
                    }
                    
                    captureSession = session;
                    try {
                        captureRequestBuilder.set(CaptureRequest.CONTROL_MODE, CaptureRequest.CONTROL_MODE_AUTO);
                        captureRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
                        
                        // 开始预览
                        final CaptureRequest captureRequest = captureRequestBuilder.build();
                        captureSession.setRepeatingRequest(captureRequest, null, backgroundHandler);
                        
                        final String formatName = (cameraFormat == ImageFormat.YUV_420_888) ? "YUV_420_888" : 
                                           (cameraFormat == ImageFormat.NV21) ? "NV21" : 
                                           "格式:" + cameraFormat;
                        
                        final StringBuilder messageBuilder = new StringBuilder(128)
                            .append("开始编码测试，摄像头格式:").append(formatName);

                        if (enableDirectPreview) {
                            messageBuilder.append("，直接预览已启用");
                        }
                        messageBuilder.append("，文件将保存到：").append(outputFile.getAbsolutePath());

                        // 使用安全的 Handler 显示Toast
                        final String message = messageBuilder.toString();
                        if (safeHandler != null) {
                            safeHandler.postToast(message, Toast.LENGTH_LONG);
                        }
                    } catch (CameraAccessException e) {
                        LogUtil.e(TAG, "相机访问异常", e);
                    }
                }

                @Override
                public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                    // 使用安全的 Handler 显示Toast
                    if (safeHandler != null) {
                        safeHandler.postToast("相机会话配置失败", Toast.LENGTH_SHORT);
                    }
                }
            }, backgroundHandler);
        } catch (CameraAccessException e) {
            LogUtil.e(TAG, "相机访问异常", e);
        }
    }

    private void encodeImageToVideo(Image image) {
        // 检查是否正在停止录像
        if (isStopping) {
            LogUtil.d(TAG, "正在停止录像，跳过此帧");
            return;
        }

        // 检查MediaCodec是否已初始化
        if (mediaCodec == null) {
            LogUtil.e(TAG, "MediaCodec尚未初始化，跳过此帧");
            return;
        }

        try {
            // 双路架构：先处理图像数据为NV12格式
            ByteBuffer nv12Data = processImageToNV12(image);
            if (nv12Data == null) {
                LogUtil.w(TAG, "NV12数据处理失败，跳过此帧");
                return;
            }

            // 再次检查停止状态，避免在处理过程中继续编码
            if (isStopping) {
                LogUtil.d(TAG, "处理过程中检测到停止信号，跳过编码");
                return;
            }

            // 路径1：发送到编码器用于录制
            sendToEncoder(nv12Data);

            // 路径2：发送到预览Surface（如果启用）
            if (enableDirectPreview && useDirectPreview && previewSurface != null && !isStopping) {
                sendToPreview(nv12Data);
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "处理视频帧失败", e);
        }
    }

    /**
     * 处理图像数据为标准NV12格式
     * @param image 相机图像数据
     * @return 处理后的NV12数据缓冲区
     */
    private ByteBuffer processImageToNV12(Image image) {
        try {
            // 获取图像尺寸信息
            final int width = image.getWidth();
            final int height = image.getHeight();

            // 更新实际图像尺寸（用于预览渲染）
            actualImageWidth = width;
            actualImageHeight = height;

            // 计算NV12数据大小
            final int ySize = width * height;
            final int uvSize = ySize / 2;
            final int totalSize = ySize + uvSize;

            // 获取或创建共享缓冲区 - 使用缓冲区池
            synchronized (bufferLock) {
                if (nv12SharedBuffer == null || nv12SharedBuffer.capacity() < totalSize) {
                    // 使用缓冲区池获取缓冲区
                    nv12SharedBuffer = getBufferFromPool();
                    if (nv12SharedBuffer.capacity() < totalSize) {
                        // 如果池中的缓冲区太小，创建新的
                        nv12SharedBuffer = ByteBuffer.allocateDirect(totalSize);
                    }
                    // LogUtil.d(TAG, "获取NV12共享缓冲区，大小: " + nv12SharedBuffer.capacity() + " 字节");
                }
                nv12SharedBuffer.clear();
            }

            // 获取平面信息
            final Image.Plane[] planes = image.getPlanes();

            // 处理Y平面
            final ByteBuffer yBuffer = planes[0].getBuffer();
            final int yDataSize = yBuffer.remaining();

            // 🚀 使用预分配缓存处理Y和UV数据 - 消除动态分配
            byte[] currentDataCache = getAvailableDataCache();

            try {
                // 直接读取Y数据到预分配缓存
                yBuffer.get(currentDataCache, 0, yDataSize);
                nv12SharedBuffer.put(currentDataCache, 0, yDataSize);

                // 处理UV平面
                final ByteBuffer uvBuffer = planes[1].getBuffer();
                final int uvDataSize = uvBuffer.remaining();

                // 直接读取UV数据到预分配缓存的后半部分
                uvBuffer.get(currentDataCache, yDataSize, uvDataSize);
                nv12SharedBuffer.put(currentDataCache, yDataSize, uvDataSize);
            } finally {
                // 释放缓存供下次使用
                releaseDataCache(currentDataCache);
            }

            nv12SharedBuffer.flip(); // 准备读取

            if (false) {
                // LogUtil.d(TAG, "NV12数据处理完成：Y平面=" + yDataSize + "字节，UV平面=" + uvDataSize + "字节，总共=" + nv12SharedBuffer.remaining() + "字节");
            }

            return nv12SharedBuffer;

        } catch (Exception e) {
            LogUtil.e(TAG, "处理图像数据为NV12格式时出错: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 发送NV12数据到编码器
     * @param nv12Data NV12格式的视频数据
     */
    private void sendToEncoder(ByteBuffer nv12Data) {
        if (mediaCodec == null || isStopping) return;

        try {
            final int inputBufferIndex = mediaCodec.dequeueInputBuffer(10000);
            if (inputBufferIndex >= 0) {
                final ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputBufferIndex);
                if (inputBuffer != null) {
                    inputBuffer.clear();

                    // 复制NV12数据到编码器输入缓冲区
                    synchronized (bufferLock) {
                        // 保存当前位置
                        int originalPosition = nv12Data.position();

                        // 重置到开始位置并复制数据
                        nv12Data.rewind();
                        inputBuffer.put(nv12Data);

                        // 恢复原始位置，确保预览路径能正确读取
                        nv12Data.position(originalPosition);

                        // 编码器数据复制完成
                    }

                    final long presentationTimeUs = (System.nanoTime() / 1000);
                    if (startTime == 0) {
                        startTime = presentationTimeUs;
                    }

                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, inputBuffer.position(),
                                              presentationTimeUs - startTime, 0);
                }
            }

            // 处理编码器输出（用于文件写入）
            processEncoderOutput();

        } catch (Exception e) {
            LogUtil.e(TAG, "发送数据到编码器失败", e);
        }
    }

    /**
     * 发送NV12数据到预览Surface
     * @param nv12Data NV12格式的视频数据
     */
    private void sendToPreview(ByteBuffer nv12Data) {
        if (previewHandler == null) {
            LogUtil.w(TAG, "预览处理器为null，跳过预览");
            return;
        }
        if (previewSurface == null) {
            LogUtil.w(TAG, "预览Surface为null，跳过预览");
            return;
        }

        // 创建数据副本，避免与编码器路径冲突
        ByteBuffer previewData = createPreviewDataCopy(nv12Data);
        if (previewData == null) {
            LogUtil.w(TAG, "创建预览数据副本失败");
            return;
        }

        // 发送数据到预览线程

        // 在预览线程中异步处理，避免阻塞编码线程
        previewHandler.post(() -> {
            try {
                renderNV12ToSurface(previewData);
            } catch (Exception e) {
                LogUtil.e(TAG, "预览渲染失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 获取预览数据引用（不创建副本，直接使用共享缓冲区）
     * @param originalData 原始NV12数据
     * @return 数据引用
     */
    private ByteBuffer createPreviewDataCopy(ByteBuffer originalData) {
        try {
            synchronized (bufferLock) {
                // 直接返回共享缓冲区的引用，避免数据复制
                // 保存当前位置并重置到开始
                int originalPosition = originalData.position();
                originalData.rewind();

                // 恢复位置供编码器使用
                originalData.position(originalPosition);

                // 返回共享缓冲区引用，不创建副本
                return originalData;
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "获取预览数据引用失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将NV12数据直接渲染到SurfaceView
     * SurfaceView原生支持YUV格式，无需转换
     * @param nv12Data NV12格式的视频数据
     */
    private void renderNV12ToSurface(ByteBuffer nv12Data) {
        try {
            if (previewSurface == null || !previewSurface.isValid()) {
                LogUtil.w(TAG, "预览Surface无效，跳过渲染");
                return;
            }

            // SurfaceView直接支持NV12数据显示
            renderNV12DirectlyToSurfaceView(nv12Data);

        } catch (Exception e) {
            LogUtil.e(TAG, "渲染NV12数据到SurfaceView失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接将NV12数据写入SurfaceView
     * 使用Android原生的Surface.lockHardwareCanvas()方法
     * @param nv12Data NV12数据
     */
    private void renderNV12DirectlyToSurfaceView(ByteBuffer nv12Data) {
        try {
            // 方法1：使用MediaCodec的方式直接输出到Surface
            // 这是最高效的方法，让GPU直接处理YUV数据
            renderNV12WithMediaCodec(nv12Data);

        } catch (Exception e) {
            LogUtil.e(TAG, "直接渲染NV12到SurfaceView失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接将NV12数据写入SurfaceView
     * 使用Surface原生的YUV支持
     * @param nv12Data NV12数据
     */
    private void renderNV12WithMediaCodec(ByteBuffer nv12Data) {
        try {
            if (!previewSurface.isValid()) {
                LogUtil.w(TAG, "Surface无效，跳过预览");
                return;
            }

            synchronized (bufferLock) {
                nv12Data.rewind();

                // 使用Surface的原生方法直接写入YUV数据
                // 这是Android系统支持的标准方式
                writeNV12ToSurface(previewSurface, nv12Data, actualImageWidth, actualImageHeight);

                // LogUtil.d(TAG, "NV12数据已直接写入SurfaceView: " + nv12Data.remaining() + " 字节");
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "直接写入NV12数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将NV12数据直接写入Surface
     * 这是核心的YUV显示方法
     * @param surface 目标Surface
     * @param nv12Data NV12数据
     * @param width 图像宽度
     * @param height 图像高度
     */
    private void writeNV12ToSurface(Surface surface, ByteBuffer nv12Data, int width, int height) {
        try {
            // 方法1：使用ImageReader方式（推荐）
            if (previewImageReader == null) {
                // 创建支持NV12格式的ImageReader
                previewImageReader = ImageReader.newInstance(width, height, ImageFormat.YUV_420_888, 2);
                previewImageReader.setOnImageAvailableListener(reader -> {
                    // 图像可用时的处理（如果需要）
                }, previewHandler);
            }

            // 方法2：直接使用Surface的lockCanvas方式
            Canvas canvas = surface.lockCanvas(null);
            if (canvas != null) {
                try {
                    // 创建YUV格式的Bitmap并直接绘制
                    renderYUVDataToCanvas(canvas, nv12Data, width, height);
                } finally {
                    surface.unlockCanvasAndPost(canvas);
                }
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "写入NV12到Surface失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将YUV数据直接渲染到Canvas
     * 修复缓冲区读取问题 + 🚀 缓存Bitmap优化
     * @param canvas 目标Canvas
     * @param nv12Data NV12数据
     * @param width 图像宽度
     * @param height 图像高度
     */
    private void renderYUVDataToCanvas(Canvas canvas, ByteBuffer nv12Data, int width, int height) {
        try {
            // 检查缓冲区大小（允许合理的差异）
            int expectedSize = width * height * 3 / 2; // NV12格式：Y + UV/2
            int actualSize = nv12Data.remaining();
            int sizeDiff = expectedSize - actualSize;

            // 移除频繁的调试日志

            // 允许最多16字节的差异（考虑内存对齐等因素）
            if (sizeDiff > 16) {
                LogUtil.w(TAG, "NV12数据不足，跳过渲染。期望:" + expectedSize + ", 实际:" + actualSize + ", 差异:" + sizeDiff);
                return;
            }

            // 数据大小在允许范围内，继续处理

            // 安全地提取数据
            nv12Data.rewind();

            // 计算实际的数据大小，适应实际缓冲区大小
            int ySize = width * height;
            int remainingAfterY = actualSize - ySize;
            int uvSize = Math.min(remainingAfterY, ySize / 2);

            // 数据分配计算完成

            // 🚀 使用预分配缓存数组 - 消除动态分配
            byte[] renderDataCache = previewDataCache; // 预览专用缓存

            // 安全读取Y数据到预分配缓存
            int availableForY = Math.min(nv12Data.remaining(), ySize);
            if (availableForY >= ySize) {
                nv12Data.get(renderDataCache, 0, ySize);
            } else {
                LogUtil.w(TAG, "Y数据不足，期望:" + ySize + ", 可用:" + availableForY);
                return;
            }

            // 安全读取UV数据到预分配缓存的后半部分
            int availableForUV = nv12Data.remaining();
            int actualUVSize = Math.min(availableForUV, uvSize);
            if (actualUVSize > 0) {
                nv12Data.get(renderDataCache, ySize, actualUVSize);
                // UV数据读取完成，如果不足会自动用0填充
            } else {
                LogUtil.w(TAG, "UV数据完全不足，跳过渲染");
                return;
            }

            // 🚀 使用预分配的NV21转换缓存
            // 注意：相机输出NV12格式，但YuvImage需要NV21，需要转换UV分量顺序
            byte[] yuvData = combineYUVDataWithUVSwap(renderDataCache, nv21ConversionCache, ySize, uvSize);
            YuvImage yuvImage = new YuvImage(yuvData, ImageFormat.NV21, width, height, null);

            // 获取Canvas尺寸
            int canvasWidth = canvas.getWidth();
            int canvasHeight = canvas.getHeight();

            // Canvas和图像尺寸信息已获取

            // 清空画布背景
            canvas.drawColor(android.graphics.Color.BLACK);

            // 🚀 缓存Bitmap优化：检查是否需要重新创建Bitmap
            if (cachedPreviewBitmap == null ||
                cachedPreviewBitmap.isRecycled() ||
                cachedPreviewBitmap.getWidth() != width ||
                cachedPreviewBitmap.getHeight() != height) {

                // 释放旧的Bitmap
                if (cachedPreviewBitmap != null && !cachedPreviewBitmap.isRecycled()) {
                    cachedPreviewBitmap.recycle();
                }

                // 重置缓存的变换矩阵
                cachedScaleMatrix = null;
            }

            // 🚀 对象复用优化：复用ByteArrayOutputStream和Rect对象
            if (cachedOutputStream == null) {
                cachedOutputStream = new java.io.ByteArrayOutputStream();
            } else {
                cachedOutputStream.reset(); // 重置而不是重新创建
            }

            if (cachedCompressRect == null ||
                cachedCompressRect.width() != width ||
                cachedCompressRect.height() != height) {
                cachedCompressRect = new Rect(0, 0, width, height);
            }

            java.io.ByteArrayOutputStream out = cachedOutputStream;
            Rect compressRect = cachedCompressRect;

            if (yuvImage.compressToJpeg(compressRect, 60, out)) {
                byte[] imageBytes = out.toByteArray();

                // 🚀 缓存Bitmap优化：复用Bitmap或创建新的
                if (cachedPreviewBitmap == null || cachedPreviewBitmap.isRecycled()) {
                    cachedPreviewBitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
                } else {
                    // 🚀 对象复用优化：复用BitmapFactory.Options对象
                    if (cachedBitmapOptions == null) {
                        cachedBitmapOptions = new BitmapFactory.Options();
                        cachedBitmapOptions.inMutable = true;
                    }

                    // 尝试复用现有Bitmap（Android 3.0+支持）
                    cachedBitmapOptions.inBitmap = cachedPreviewBitmap;
                    try {
                        Bitmap newBitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length, cachedBitmapOptions);
                        if (newBitmap != null) {
                            cachedPreviewBitmap = newBitmap;
                        } else {
                            // 复用失败，创建新的
                            cachedPreviewBitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
                        }
                    } catch (Exception e) {
                        // 复用失败，创建新的
                        cachedPreviewBitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
                    }
                }

                if (cachedPreviewBitmap != null) {
                    // 🚀 缓存变换矩阵优化：只在尺寸变化时重新计算
                    if (cachedScaleMatrix == null) {
                        cachedScaleMatrix = calculateScaleMatrix(canvasWidth, canvasHeight, width, height);
                    }

                    // 🚀 缓存Paint对象优化
                    if (cachedBitmapPaint == null) {
                        cachedBitmapPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);
                    }

                    // 使用缓存的对象进行绘制
                    canvas.drawBitmap(cachedPreviewBitmap, cachedScaleMatrix, cachedBitmapPaint);
                    // YUV数据渲染完成 - 使用缓存优化
                } else {
                    LogUtil.w(TAG, "Bitmap解码失败");
                }
            } else {
                LogUtil.w(TAG, "YuvImage压缩失败");
            }

            out.close();

        } catch (Exception e) {
            LogUtil.e(TAG, "渲染YUV数据到Canvas失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🚀 优化版：计算缩放变换矩阵（缓存复用）
     * @param canvasWidth Canvas宽度
     * @param canvasHeight Canvas高度
     * @param bitmapWidth Bitmap宽度
     * @param bitmapHeight Bitmap高度
     * @return 缓存的变换矩阵
     */
    private Matrix calculateScaleMatrix(int canvasWidth, int canvasHeight, int bitmapWidth, int bitmapHeight) {
        Matrix matrix = new Matrix();

        // 拉伸铺满模式：分别计算X和Y方向的缩放比例，不保持宽高比
        // 这样可以让图像完全铺满预览框，消除黑边
        float scaleX = (float) canvasWidth / bitmapWidth;
        float scaleY = (float) canvasHeight / bitmapHeight;

        // 直接使用各自的缩放比例，实现拉伸铺满效果
        // 不再使用Math.min，而是分别在X和Y方向进行缩放

        // 设置变换矩阵：分别在X和Y方向缩放，无需平移（因为铺满整个画布）
        matrix.setScale(scaleX, scaleY);
        // 不需要平移，因为图像已经铺满整个画布

        return matrix;
    }

    /**
     * 将Bitmap拉伸铺满绘制到Canvas上
     * @param canvas 目标Canvas
     * @param bitmap 源Bitmap
     * @param canvasWidth Canvas宽度
     * @param canvasHeight Canvas高度
     */
    private void drawBitmapWithScaleAndCenter(Canvas canvas, Bitmap bitmap, int canvasWidth, int canvasHeight) {
        int bitmapWidth = bitmap.getWidth();
        int bitmapHeight = bitmap.getHeight();

        // 拉伸铺满模式：分别计算X和Y方向的缩放比例，不保持宽高比
        // 这样可以让图像完全铺满预览框，消除黑边
        float scaleX = (float) canvasWidth / bitmapWidth;
        float scaleY = (float) canvasHeight / bitmapHeight;

        // 直接使用各自的缩放比例，实现拉伸铺满效果
        int scaledWidth = canvasWidth;   // 直接使用画布宽度
        int scaledHeight = canvasHeight; // 直接使用画布高度

        // 由于是铺满整个画布，起始位置为(0,0)
        int left = 0;
        int top = 0;

        // 创建目标矩形 - 铺满整个画布
        Rect destRect = new Rect(left, top, left + scaledWidth, top + scaledHeight);

        // 绘制参数计算完成 - 拉伸铺满模式

        // 使用高质量绘制
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setFilterBitmap(true);
        paint.setDither(true);

        // 绘制拉伸后的图像，铺满整个画布
        canvas.drawBitmap(bitmap, null, destRect, paint);
    }

    /**
     * 🚀 组合Y和UV数据为NV21格式（交换UV分量）
     * 相机输出NV12(UVUV...)，YuvImage需要NV21(VUVU...)
     * 使用预分配转换缓存，避免额外的数组分配
     */
    private byte[] combineYUVDataWithUVSwap(byte[] sourceData, byte[] conversionCache, int ySize, int uvSize) {
        // 先复制Y数据到转换缓存
        System.arraycopy(sourceData, 0, conversionCache, 0, ySize);

        // 复制UV数据并交换分量：NV12(UVUV...) → NV21(VUVU...)
        int sourceUvOffset = ySize;
        int targetUvOffset = ySize;
        for (int i = 0; i < uvSize; i += 2) {
            if (i + 1 < uvSize) {
                // 交换U和V分量
                conversionCache[targetUvOffset + i] = sourceData[sourceUvOffset + i + 1];     // V分量
                conversionCache[targetUvOffset + i + 1] = sourceData[sourceUvOffset + i];     // U分量
            }
        }

        // 返回转换缓存数组
        return conversionCache;
    }

    /**
     * 组合Y和UV数据为标准YUV格式（保持原始顺序）
     */
    private byte[] combineYUVData(byte[] yData, byte[] uvData) {
        byte[] yuv = new byte[yData.length + uvData.length];
        System.arraycopy(yData, 0, yuv, 0, yData.length);
        System.arraycopy(uvData, 0, yuv, yData.length, uvData.length);
        return yuv;
    }



    /**
     * 从缓冲区池获取缓冲区
     */
    private ByteBuffer getBufferFromPool() {
        synchronized (bufferLock) {
            ByteBuffer buffer = bufferPool.poll();
            if (buffer == null) {
                // 池中没有可用缓冲区，创建新的
                final int ySize = FIXED_WIDTH * FIXED_HEIGHT;
                final int uvSize = ySize / 2;
                final int totalSize = ySize + uvSize;
                buffer = ByteBuffer.allocateDirect(totalSize);
                // LogUtil.d(TAG, "缓冲区池已满，创建新缓冲区");
            }
            return buffer;
        }
    }

    /**
     * 归还缓冲区到池中
     */
    private void returnBufferToPool(ByteBuffer buffer) {
        if (buffer != null) {
            synchronized (bufferLock) {
                if (bufferPool.size() < BUFFER_POOL_SIZE) {
                    buffer.clear();
                    bufferPool.offer(buffer);
                }
                // 如果池已满，让GC回收这个缓冲区
            }
        }
    }




            
    /**
     * 处理编码器输出（仅用于文件写入，不再处理解码预览）
     */
    private void processEncoderOutput() {
        if (mediaCodec == null || isStopping) return;

        try {
            // 获取编码后的数据
            final MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
            int outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10000);
            
            // 减少分段检查频率，只在关键时间间隔检查
            long currentTimeMs = System.currentTimeMillis();
            if (segmentRecording && muxerStarted && 
                (currentTimeMs - lastSegmentCheckTimeMs >= SEGMENT_CHECK_INTERVAL_MS)) {
                
                lastSegmentCheckTimeMs = currentTimeMs;
                
                // 检查是否需要切换到新片段
                if (currentTimeMs - segmentStartTimeMs > segmentDurationMs) {
                    // 只在关键帧处切换片段，提高效率并避免视频问题
                    switchSegmentOnNextKeyFrame = true;
                }
            }
            
            // 首帧处理标志
            boolean receivedKeyFrame = false;
            
            while (outputBufferIndex >= 0) {
                final ByteBuffer outputBuffer = mediaCodec.getOutputBuffer(outputBufferIndex);
                
                // 特殊处理编码器配置数据
                boolean isConfigFrame = (bufferInfo.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0;
                boolean isKeyFrame = (bufferInfo.flags & MediaCodec.BUFFER_FLAG_KEY_FRAME) != 0;
                
                // 在关键帧处切换片段（如果需要）
                if (isKeyFrame && switchSegmentOnNextKeyFrame) {
                    try {
                        asyncSwitchToNextSegment();
                        switchSegmentOnNextKeyFrame = false;
                        // 处理完当前帧后继续
                    } catch (Exception e) {
                        LogUtil.e(TAG, "切换片段失败: " + e.getMessage());
                        switchSegmentOnNextKeyFrame = false;
                    }
                }
                
                if (isConfigFrame) {
                    // 对于文件写入，忽略配置帧
                    bufferInfo.size = 0;
                    // LogUtil.d(TAG, "跳过编码器配置帧");
                }
                
                if (isKeyFrame) {
                    receivedKeyFrame = true;
                    // 降低日志频率，只在调试时输出
                    if (false) {
                        // LogUtil.d(TAG, "收到关键帧");
                    }
                }
                
                if (bufferInfo.size > 0) {
                    if (!muxerStarted) {
                        // 获取编码器输出格式
                        final MediaFormat format = mediaCodec.getOutputFormat();
                        videoTrackIndex = mediaMuxer.addTrack(format);
                        mediaMuxer.start();
                        muxerStarted = true;
                        // LogUtil.d(TAG, "MediaMuxer已启动, 视频轨道: " + videoTrackIndex);
                    }
                    
                    // 将编码后的数据写入muxer
                    if (outputBuffer != null) {
                        outputBuffer.position(bufferInfo.offset);
                        outputBuffer.limit(bufferInfo.offset + bufferInfo.size);
                        mediaMuxer.writeSampleData(videoTrackIndex, outputBuffer, bufferInfo);

                        if (false) {
                            // LogUtil.d(TAG, "写入视频数据: " + bufferInfo.size + " 字节");
                        }
                    }
                }
                
                mediaCodec.releaseOutputBuffer(outputBufferIndex, false);
                outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10000);
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "处理编码器输出失败", e);
        }
    }


    public void stopTest() {
        LogUtil.i(TAG, "开始停止录像...");

        // 首先设置停止标志位，防止新的帧处理
        isStopping = true;

        try {
            // 等待当前正在处理的帧完成
            int waitCount = 0;
            while (isProcessingFrame && waitCount < 50) { // 最多等待500ms
                try {
                    Thread.sleep(10);
                    waitCount++;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            // 停止相机预览（优先停止数据源）
            if (captureSession != null) {
                try {
                    captureSession.stopRepeating();
                    captureSession.close();
                    LogUtil.d(TAG, "相机捕获会话已停止");
                } catch (Exception e) {
                    LogUtil.w(TAG, "停止相机捕获会话时出错", e);
                }
                captureSession = null;
            }

            // 关闭相机设备
            if (cameraDevice != null) {
                try {
                    cameraDevice.close();
                    LogUtil.d(TAG, "相机设备已关闭");
                } catch (Exception e) {
                    LogUtil.w(TAG, "关闭相机设备时出错", e);
                }
                cameraDevice = null;
            }

            // 关闭图像读取器
            if (imageReader != null) {
                try {
                    imageReader.close();
                    LogUtil.d(TAG, "图像读取器已关闭");
                } catch (Exception e) {
                    LogUtil.w(TAG, "关闭图像读取器时出错", e);
                }
                imageReader = null;
            }

            // 停止并释放编码器
            if (mediaCodec != null) {
                try {
                    mediaCodec.stop();
                    mediaCodec.release();
                    LogUtil.d(TAG, "MediaCodec已停止并释放");
                } catch (Exception e) {
                    LogUtil.w(TAG, "停止MediaCodec时出错", e);
                }
                mediaCodec = null;
            }

            // 停止并释放混合器
            if (muxerStarted && mediaMuxer != null) {
                try {
                    mediaMuxer.stop();
                    mediaMuxer.release();
                    LogUtil.d(TAG, "MediaMuxer已停止并释放");
                } catch (Exception e) {
                    LogUtil.w(TAG, "停止MediaMuxer时出错", e);
                } finally {
                    // 无论是否异常，都要尝试重命名文件
                    renameTempFileToFinal();
                }
                mediaMuxer = null;
                muxerStarted = false;
            }

            // 停止预览线程
            stopPreviewThread();

            // 停止后台线程
            stopBackgroundThread();

            // 重置状态标志
            isProcessingFrame = false;
            frameSkipCounter = 0;
            startTime = 0;
            videoTrackIndex = -1;

            LogUtil.i(TAG, "录像停止完成");

            // 使用安全的 Handler 显示Toast
            if (outputFile != null && safeHandler != null) {
                final String filePath = outputFile.getAbsolutePath();
                safeHandler.postToast("视频保存成功：" + filePath, Toast.LENGTH_LONG);
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "停止录像时发生错误", e);
        } finally {
            // 确保停止标志位被重置（为下次录制做准备）
            // 注意：这里不重置isStopping，因为对象可能会被重用
        }
    }
    
    private void cleanup() {
        try {
            if (cameraDevice != null) {
                cameraDevice.close();
                cameraDevice = null;
            }

            if (mediaCodec != null) {
                mediaCodec.release();
                mediaCodec = null;
            }

            if (mediaMuxer != null) {
                if (muxerStarted) {
                    mediaMuxer.stop();
                    // 录制完成，将临时文件重命名为最终文件
                    renameTempFileToFinal();
                }
                mediaMuxer.release();
                mediaMuxer = null;
            }

            if (imageReader != null) {
                imageReader.close();
                imageReader = null;
            }

            stopBackgroundThread();
            stopPreviewThread();

            // 清理缓冲区池
            synchronized (bufferLock) {
                // 归还共享缓冲区到池中
                if (nv12SharedBuffer != null) {
                    returnBufferToPool(nv12SharedBuffer);
                    nv12SharedBuffer = null;
                }
                bufferPool.clear();
            }

            // 🚀 清理缓存的Bitmap和相关对象
            if (cachedPreviewBitmap != null && !cachedPreviewBitmap.isRecycled()) {
                cachedPreviewBitmap.recycle();
                cachedPreviewBitmap = null;
            }
            cachedScaleMatrix = null;
            cachedBitmapPaint = null;

            // 🚀 清理预分配缓存（设为null让GC回收）
            primaryDataCache = null;
            secondaryDataCache = null;
            previewDataCache = null;
            yPlaneCache = null;
            uvPlaneCache = null;
            nv21ConversionCache = null;
            jpegCompressionCache = null;

            // 重置缓存状态标记
            primaryCacheInUse = false;
            secondaryCacheInUse = false;
            previewCacheInUse = false;

            // 重置录像状态标记
            isStopping = false;
            isProcessingFrame = false;
            frameSkipCounter = 0;
            muxerStarted = false;

            // 清理 Handler 避免内存泄漏
            if (safeHandler != null) {
                safeHandler.removeCallbacksAndMessages(null);
                safeHandler = null;
            }

            // 清理 Context 引用
            context = null;
            applicationContext = null;
            if (cachedOutputStream != null) {
                try {
                    cachedOutputStream.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
                cachedOutputStream = null;
            }
            cachedCompressRect = null;
            cachedBitmapOptions = null;

        } catch (Exception e) {
            LogUtil.e(TAG, "清理资源时发生错误", e);
        }
    }
    
    private void stopBackgroundThread() {
        if (backgroundThread != null) {
            // 清理 Handler 消息队列
            if (backgroundHandler != null) {
                backgroundHandler.removeCallbacksAndMessages(null);
                backgroundHandler = null;
            }

            backgroundThread.quitSafely();
            try {
                // 设置超时，避免无限等待
                backgroundThread.join(3000);

                // 检查线程是否仍在运行
                if (backgroundThread.isAlive()) {
                    LogUtil.w(TAG, "后台线程停止超时，强制中断");
                    backgroundThread.interrupt();
                    // 再等待一小段时间让线程响应中断
                    backgroundThread.join(1000);
                }
                backgroundThread = null;
            } catch (InterruptedException e) {
                LogUtil.e(TAG, "停止后台线程被中断", e);
                Thread.currentThread().interrupt(); // 恢复中断状态
            }
        }
    }

    /**
     * 停止预览线程
     */
    private void stopPreviewThread() {
        if (previewThread != null) {
            // 清理 Handler 消息队列
            if (previewHandler != null) {
                previewHandler.removeCallbacksAndMessages(null);
                previewHandler = null;
            }

            previewThread.quitSafely();
            try {
                // 设置超时，避免无限等待
                previewThread.join(3000);

                // 检查线程是否仍在运行
                if (previewThread.isAlive()) {
                    LogUtil.w(TAG, "预览线程停止超时，强制中断");
                    previewThread.interrupt();
                    // 再等待一小段时间让线程响应中断
                    previewThread.join(1000);
                }
                previewThread = null;
                LogUtil.d(TAG, "预览线程已停止");
            } catch (InterruptedException e) {
                LogUtil.e(TAG, "停止预览线程被中断", e);
                Thread.currentThread().interrupt(); // 恢复中断状态
            }
        }
    }

    public void setRecordPath(String path) {
        this.recordPath = path;
        // 确保路径存在
        File directory = new File(path);
        if (!directory.exists()) {
            directory.mkdirs();
        }
    }
    
    /**
     * 设置视频码率
     * @param bitRate 码率值，单位为bps，例如1000000表示1Mbps
     */
    public void setBitRate(int bitRate) {
        if (bitRate > 0) {
            this.bitRateValue = bitRate;
            LogUtil.i(TAG, "设置视频码率: " + bitRate + "bps");
        }
    }
    
    /**
     * 设置是否启用分段录制和分段时长
     * @param enable 是否启用分段录制
     * @param durationMinutes 每个片段的时长(分钟)，默认为10分钟
     */
    public void setSegmentRecording(boolean enable, int durationMinutes) {
        this.segmentRecording = enable;
        if (durationMinutes > 0) {
            this.segmentDurationMs = durationMinutes * 60 * 1000;
        }
        LogUtil.i(TAG, "分段录制: " + (enable ? "已启用" : "已禁用") + 
              (enable ? "，每段时长: " + durationMinutes + "分钟" : ""));
    }

    // 切换到新的视频分段，保持无缝录制
    private void switchToNextSegment() {
        try {
            LogUtil.i(TAG, "开始切换到新片段...");
            
            // 记录切换开始时间，用于性能优化
            long switchStartTime = System.currentTimeMillis();
            
            // 1. 获取当前编码器格式
            final MediaFormat currentFormat = mediaCodec.getOutputFormat();
            
            // 2. 停止当前的mediaMuxer
            if (muxerStarted && mediaMuxer != null) {
                try {
                    mediaMuxer.stop();
                    mediaMuxer.release();
                } catch (Exception e) {
                    LogUtil.w(TAG, "切换片段时停止MediaMuxer出错", e);
                } finally {
                    // 无论是否异常，都要尝试重命名文件
                    renameTempFileToFinal();
                }

                // 使用Handler在主线程显示Toast，避免阻塞当前线程
                final String savedFilePath = finalOutputFile != null ? finalOutputFile.getAbsolutePath() : outputFile.getAbsolutePath();
                new Handler(Looper.getMainLooper()).post(() -> {
                    Toast.makeText(context, "已保存视频片段" + segmentCount + ": " + savedFilePath,
                        Toast.LENGTH_SHORT).show();
                });
            }
            
            // 3. 创建新的MediaMuxer
            segmentCount++;
            
            // 生成新的时间戳 - 使用SimpleDateFormat的缓存实例，减少对象创建
            final String timestamp = DATE_FORMAT.format(new Date());
            
            // 提取摄像头方向名称 - 使用更高效的方式获取摄像头方向
            String cameraDirection = getCameraDirectionName(cameraId);
            
            // 更新文件名，包含新时间戳（去掉片段序号）
            baseFileName = cameraDirection + "_" + timestamp;

            final String extension = codecType.equals("H.264") ? ".mp4" : ".mkv";
            String tempFileName = baseFileName + ".tmp";
            String finalFileName = baseFileName + extension;

            File directory = outputFile.getParentFile();
            outputFile = new File(directory, tempFileName);
            finalOutputFile = new File(directory, finalFileName);
            
            mediaMuxer = new MediaMuxer(outputFile.getAbsolutePath(), MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            
            // 4. 重新添加轨道
            videoTrackIndex = mediaMuxer.addTrack(currentFormat);
            
            // 5. 启动新的mediaMuxer
            mediaMuxer.start();
            
            // 重置片段开始时间
            segmentStartTimeMs = System.currentTimeMillis();
            
            // 记录片段切换耗时
            long switchDuration = System.currentTimeMillis() - switchStartTime;
            LogUtil.i(TAG, "成功切换到新片段 " + segmentCount + ": " + outputFile.getAbsolutePath() + 
                   " (耗时: " + switchDuration + "ms)");
        } catch (IOException e) {
            LogUtil.e(TAG, "切换视频片段失败", e);
            // 如果切换失败，关闭分段录制
            segmentRecording = false;
        }
    }
    
    // 从摄像头ID获取方向名称的辅助方法
    private String getCameraDirectionName(String cameraId) {
        switch (cameraId) {
            case "26": return "前视";
            case "30": return "后视";
            case "34": return "左视";
            case "38": return "右视";
            default: return "摄像头" + cameraId;
        }
    }

    /**
     * 将临时文件重命名为最终文件
     */
    private void renameTempFileToFinal() {
        if (outputFile != null && finalOutputFile != null) {
            if (!outputFile.exists()) {
                LogUtil.w(TAG, "临时文件不存在，无法重命名: " + outputFile.getName());
                return;
            }

            try {
                // 如果目标文件已存在，先删除
                if (finalOutputFile.exists()) {
                    boolean deleted = finalOutputFile.delete();
                    LogUtil.d(TAG, "删除已存在的目标文件: " + deleted);
                }

                boolean success = outputFile.renameTo(finalOutputFile);
                if (success) {
                    LogUtil.d(TAG, "文件重命名成功: " + outputFile.getName() + " -> " + finalOutputFile.getName());
                } else {
                    LogUtil.w(TAG, "文件重命名失败: " + outputFile.getName() + " -> " + finalOutputFile.getName());
                    // 重命名失败时，至少保留临时文件
                    LogUtil.i(TAG, "临时文件保留: " + outputFile.getAbsolutePath());
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "文件重命名异常: " + outputFile.getName(), e);
            }
        } else {
            LogUtil.w(TAG, "文件对象为空，无法重命名");
        }
    }

    // 使用AsyncTask来异步执行片段切换，减少对主线程的阻塞
    private void asyncSwitchToNextSegment() {
        new Thread(() -> {
            try {
                switchToNextSegment();
            } catch (Exception e) {
                LogUtil.e(TAG, "异步切换片段失败: " + e.getMessage());
                switchSegmentOnNextKeyFrame = false;
            }
        }).start();
    }

    // 直接处理图像数据，专为NV12格式优化
    private void directCopyImageToBuffer(Image image, ByteBuffer inputBuffer) {
        try {
            // 获取图像尺寸信息
            final int width = image.getWidth();
            final int height = image.getHeight();
            
            // 清除输入缓冲区
            inputBuffer.clear();
            
            // 获取平面信息
            final Image.Plane[] planes = image.getPlanes();
            
            // 处理NV12格式 (Y平面 + UV交错平面)
            // 1. 复制Y平面 - 直接使用剩余字节数
            final ByteBuffer yBuffer = planes[0].getBuffer();
            final int ySize = yBuffer.remaining();
            
            // 🚀 使用预分配缓存处理Y和UV数据 - 消除动态分配
            byte[] directCopyCache = getAvailableDataCache();

            try {
                // 读取Y数据到预分配缓存
                yBuffer.get(directCopyCache, 0, ySize);
                inputBuffer.put(directCopyCache, 0, ySize);

                // 2. 复制UV平面 (交错存储的U和V) - 直接使用剩余字节数
                final ByteBuffer uvBuffer = planes[1].getBuffer();
                final int uvSize = uvBuffer.remaining();

                // 读取UV数据到预分配缓存的后半部分
                uvBuffer.get(directCopyCache, ySize, uvSize);
                inputBuffer.put(directCopyCache, ySize, uvSize);
            } finally {
                // 释放缓存供下次使用
                releaseDataCache(directCopyCache);
            }
            
            if (false) {
                // LogUtil.d(TAG, "NV12数据已复制：Y平面=" + ySize + "字节，UV平面=" + uvSize + "字节，总共=" + inputBuffer.position() + "字节");
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "复制图像数据时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 更新预览Surface
     * 在配置变化时调用，避免重新创建编码器
     */
    public void updatePreviewSurface(Surface newSurface) {
        if (this.previewSurface != newSurface) {
            this.previewSurface = newSurface;

            // 更新直接预览状态
            if (newSurface != null && newSurface.isValid()) {
                enableDirectPreview = true;
                LogUtil.i(TAG, "预览Surface已更新，启用直接预览");
            } else {
                enableDirectPreview = false;
                LogUtil.i(TAG, "预览Surface已清除或无效，禁用直接预览");
            }
        }
    }

    /**
     * 暂停预览但保持录制
     */
    public void pausePreview() {
        enableDirectPreview = false;
        LogUtil.i(TAG, "预览已暂停，录制继续");
    }

    /**
     * 恢复预览
     */
    public void resumePreview(Surface surface) {
        if (surface != null && surface.isValid()) {
            this.previewSurface = surface;
            enableDirectPreview = true;

            // 确保预览线程已初始化
            if (previewThread == null) {
                initPreviewThread();
            }

            LogUtil.i(TAG, "预览已恢复");
        } else {
            LogUtil.w(TAG, "无法恢复预览，Surface无效");
        }
    }
} 