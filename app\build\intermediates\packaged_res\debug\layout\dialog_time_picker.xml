<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background_shape"
    android:padding="20dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="选择时间"
        android:textSize="20sp"
        android:textColor="@color/dialog_label_text_color"
        android:gravity="center"
        android:paddingBottom="20dp"
        android:fontFamily="sans-serif-medium" />

    <!-- NumberPicker容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- 月份选择器 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="月份"
                android:textSize="18sp"
                android:textColor="@color/dialog_label_text_color"
                android:gravity="center"
                android:paddingBottom="15dp" />

            <NumberPicker
                android:id="@+id/month_picker"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_gravity="center"
                style="@style/TimePickerNumberPickerStyle" />

        </LinearLayout>

        <!-- 第一个分隔线 -->
        <View
            android:layout_width="2dp"
            android:layout_height="200dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginVertical="30dp"
            android:background="@color/separator_line_color" />

        <!-- 日期选择器 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="日期"
                android:textSize="18sp"
                android:textColor="@color/dialog_label_text_color"
                android:gravity="center"
                android:paddingBottom="15dp" />

            <NumberPicker
                android:id="@+id/day_picker"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_gravity="center"
                style="@style/TimePickerNumberPickerStyle" />

        </LinearLayout>

        <!-- 第二个分隔线 -->
        <View
            android:layout_width="2dp"
            android:layout_height="200dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginVertical="30dp"
            android:background="@color/separator_line_color" />

        <!-- 小时选择器 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="小时"
                android:textSize="18sp"
                android:textColor="@color/dialog_label_text_color"
                android:gravity="center"
                android:paddingBottom="15dp" />

            <NumberPicker
                android:id="@+id/hour_picker"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_gravity="center"
                style="@style/TimePickerNumberPickerStyle" />

        </LinearLayout>

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:paddingTop="20dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="10dp"
            android:text="取消"
            style="@style/DialogButtonStyle" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:text="确定"
            style="@style/DialogButtonStyle" />

    </LinearLayout>

</LinearLayout>
