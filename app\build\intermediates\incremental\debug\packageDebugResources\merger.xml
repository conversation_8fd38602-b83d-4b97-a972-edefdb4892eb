<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\XM\rsbjk-a\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\XM\rsbjk-a\app\src\main\res"><file name="button_background" path="E:\XM\rsbjk-a\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_outline" path="E:\XM\rsbjk-a\app\src\main\res\drawable\button_outline.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\XM\rsbjk-a\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\XM\rsbjk-a\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="play_pause_button_state" path="E:\XM\rsbjk-a\app\src\main\res\drawable\play_pause_button_state.xml" qualifiers="" type="drawable"/><file name="settings_icon" path="E:\XM\rsbjk-a\app\src\main\res\drawable\settings_icon.xml" qualifiers="" type="drawable"/><file name="text_stroke_background" path="E:\XM\rsbjk-a\app\src\main\res\drawable\text_stroke_background.xml" qualifiers="" type="drawable"/><file name="activity_camera_preview" path="E:\XM\rsbjk-a\app\src\main\res\layout\activity_camera_preview.xml" qualifiers="" type="layout"/><file name="activity_main" path="E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="item_video_record" path="E:\XM\rsbjk-a\app\src\main\res\layout\item_video_record.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\XM\rsbjk-a\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\XM\rsbjk-a\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="colorAccent">#FF4081</color><color name="white">#FFFFFF</color><color name="background_light">#DEE2E5</color><color name="text_primary_adaptive">#000000</color><color name="text_secondary_adaptive">#666666</color><color name="background_primary_adaptive">#DEE2E5</color><color name="background_secondary_adaptive">#DEE2E5</color><color name="container_background_adaptive">#F5F5F5</color><color name="button_text_selected_adaptive">#000000</color><color name="button_text_unselected_adaptive">#808080</color><color name="status_bar_color">#DEE2E5</color><color name="button_background_selected">#44676767</color><color name="button_outline_color">@color/colorPrimary</color><color name="number_picker_text_color">#000000</color><color name="separator_line_color">#9C9C9C</color><color name="dialog_label_text_color">#9C9C9C</color><color name="dialog_background_color">#CED0D1</color><color name="player_control_text_color">#FFFFFF</color><color name="text_adaptive">@color/text_primary_adaptive</color><color name="button_text_unselected">@color/button_text_unselected_adaptive</color></file><file path="E:\XM\rsbjk-a\app\src\main\res\values\dimens.xml" qualifiers=""/><file path="E:\XM\rsbjk-a\app\src\main\res\values\picker_styles.xml" qualifiers=""><style name="NumberPickerStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/number_picker_text_color</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:gravity">center</item>
    </style></file><file path="E:\XM\rsbjk-a\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">哨兵监控</string></file><file path="E:\XM\rsbjk-a\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Sbjk" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        
        <item name="colorAccent">@color/colorAccent</item>
        
        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:navigationBarColor">@color/colorPrimaryDark</item>
        <item name="android:statusBarColor">@color/background_light</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">true</item>
        <item name="android:windowBackground">@color/background_light</item>
    </style><style name="CustomButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
        <item name="android:textColor">@color/colorPrimary</item>  
        <item name="android:drawableTint">@color/white</item>     
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:includeFontPadding">false</item>
    </style></file><file path="E:\XM\rsbjk-a\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="text_primary_adaptive">#FFFFFF</color><color name="text_secondary_adaptive">#CCCCCC</color><color name="background_primary_adaptive">#202020</color><color name="background_secondary_adaptive">#202020</color><color name="container_background_adaptive">#1A1A1A</color><color name="button_text_selected_adaptive">#FFFFFF</color><color name="button_text_unselected_adaptive">#808080</color><color name="status_bar_color">#202020</color><color name="button_background_selected">#44676767</color><color name="button_outline_color">#FFFFFF</color><color name="number_picker_text_color">#000000</color><color name="separator_line_color">#D8D8D8</color><color name="dialog_label_text_color">#D8D8D8</color><color name="dialog_background_color">#858585</color><color name="player_control_text_color">#FFFFFF</color><color name="text_adaptive">@color/text_primary_adaptive</color><color name="button_text_unselected">@color/button_text_unselected_adaptive</color><color name="background_light">#323232</color></file><file path="E:\XM\rsbjk-a\app\src\main\res\values-night\picker_styles.xml" qualifiers="night-v8"><style name="NumberPickerStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/number_picker_text_color</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:gravity">center</item>
    </style></file><file path="E:\XM\rsbjk-a\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Sbjk" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        
        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:navigationBarColor">@color/background_light</item>
        <item name="android:statusBarColor">@color/background_light</item>
        <item name="android:windowBackground">@color/background_light</item>
    </style></file><file name="backup_rules" path="E:\XM\rsbjk-a\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\XM\rsbjk-a\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\XM\rsbjk-a\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\XM\rsbjk-a\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\XM\rsbjk-a\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\XM\rsbjk-a\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>