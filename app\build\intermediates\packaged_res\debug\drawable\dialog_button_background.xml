<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/button_pressed_background_color" />
            <corners android:radius="4dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/button_normal_background_color" />
            <corners android:radius="4dp" />
            <stroke
                android:width="1dp"
                android:color="@color/separator_line_color" />
        </shape>
    </item>
    
</selector>
