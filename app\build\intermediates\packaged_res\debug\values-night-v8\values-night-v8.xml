<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_adaptive">#A1A1A1</color>
    <color name="background_light">#323232</color>
    <color name="background_primary_adaptive">#202020</color>
    <color name="background_secondary_adaptive">#202020</color>
    <color name="button_background_selected">#44676767</color>
    <color name="button_normal_background_color">#404040</color>
    <color name="button_outline_color">#FFFFFF</color>
    <color name="button_pressed_background_color">#606060</color>
    <color name="button_text_color">#FFFFFF</color>
    <color name="button_text_selected_adaptive">#FFFFFF</color>
    <color name="button_text_unselected">@color/button_text_unselected_adaptive</color>
    <color name="button_text_unselected_adaptive">#808080</color>
    <color name="container_background_adaptive">#1A1A1A</color>
    <color name="dialog_background_color">#000000</color>
    <color name="dialog_button_text_color">#FFFFFF</color>
    <color name="dialog_label_text_color">#000000</color>
    <color name="navigation_bar_color">#202020</color>
    <color name="number_picker_text_color">#000000</color>
    <color name="player_control_background_color">#CC000000</color>
    <color name="player_control_text_color">#FFFFFF</color>
    <color name="separator_line_color">#0B0B0B</color>
    <color name="status_bar_color">#202020</color>
    <color name="text_adaptive">@color/text_primary_adaptive</color>
    <color name="text_primary_adaptive">#FFFFFF</color>
    <color name="text_secondary_adaptive">#CCCCCC</color>
    <color name="window_background">#202020</color>
    <style name="Base.Theme.Sbjk" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="DialogButtonStyle">
        <item name="android:textColor">@color/dialog_button_text_color</item>
        <item name="android:background">@drawable/dialog_button_background</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:gravity">center</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="NumberPickerStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/number_picker_text_color</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="Theme.Sbjk" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        
        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:navigationBarColor">@color/background_light</item>
        <item name="android:statusBarColor">@color/background_light</item>
        <item name="android:windowBackground">@color/background_light</item>
    </style>
    <style name="TimePickerNumberPickerStyle">
        <item name="android:textSize">23sp</item>
        <item name="android:textColor">@color/number_picker_text_color</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:gravity">center</item>
        <item name="android:descendantFocusability">blocksDescendants</item>
        <item name="android:orientation">vertical</item>
    </style>
</resources>