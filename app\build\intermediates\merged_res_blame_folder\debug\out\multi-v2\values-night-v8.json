{"logs": [{"outputFile": "com.autolink.sbjk.app-mergeDebugResources-35:/values-night-v8/values-night-v8.xml", "map": [{"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\picker_styles.xml", "from": {"startLines": "23,-1,13", "startColumns": "4,-1,4", "startOffsets": "957,-1,481", "endLines": "30,-1,20", "endColumns": "12,-1,12", "endOffsets": "1379,-1,923"}, "to": {"startLines": "32,40,95", "startColumns": "4,4,4", "startOffsets": "1739,2166,6926", "endLines": "39,45,102", "endColumns": "12,12,12", "endOffsets": "2161,2464,7368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,47,48,49,50,51,52,93", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2469,2539,2623,2707,2803,2905,3007,6720", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "2534,2618,2702,2798,2900,3002,3096,6804"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,8", "startColumns": "4,4", "startOffsets": "100,365", "endLines": "5,21", "endColumns": "12,12", "endOffsets": "311,1133"}, "to": {"startLines": "28,79", "startColumns": "4,4", "startOffsets": "1627,5989", "endLines": "31,92", "endColumns": "12,12", "endOffsets": "1734,6715"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "63,18,-1,-1,-1,55,-1,57,-1,-1,-1,-1,-1,43,53,41,-1,37,-1,-1,39,-1,-1,-1,-1,-1", "startColumns": "4,4,-1,-1,-1,4,-1,4,-1,-1,-1,-1,-1,4,4,4,-1,4,-1,-1,4,-1,-1,-1,-1,-1", "startOffsets": "3364,1137,-1,-1,-1,2944,-1,3070,-1,-1,-1,-1,-1,2360,2824,2241,-1,1993,-1,-1,2116,-1,-1,-1,-1,-1", "endColumns": "53,50,-1,-1,-1,64,-1,65,-1,-1,-1,-1,-1,57,58,57,-1,58,-1,-1,54,-1,-1,-1,-1,-1", "endOffsets": "3413,1183,-1,-1,-1,3004,-1,3131,-1,-1,-1,-1,-1,2413,2878,2294,-1,2047,-1,-1,2166,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,160,222,286,349,414,469,535,587,651,739,805,869,927,986,1044,1099,1158,1226,1286,1341,1392,1461,1517,1575", "endColumns": "53,50,61,63,62,64,54,65,51,63,87,65,63,57,58,57,54,58,67,59,54,50,68,55,57,51", "endOffsets": "104,155,217,281,344,409,464,530,582,646,734,800,864,922,981,1039,1094,1153,1221,1281,1336,1387,1456,1512,1570,1622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,94,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3101,3176,3287,3376,3477,3584,3691,3790,3897,4000,4127,4215,4339,4441,4543,4659,4761,4875,5003,5119,5241,5377,5497,5631,5751,5863,6809,7373,7497,7627,7749,7887,8021,8137", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "3171,3282,3371,3472,3579,3686,3785,3892,3995,4122,4210,4334,4436,4538,4654,4756,4870,4998,5114,5236,5372,5492,5626,5746,5858,5984,6921,7492,7622,7744,7882,8016,8132,8252"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-mergeDebugResources-35:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\picker_styles.xml", "from": {"startLines": "4", "startColumns": "4", "startOffsets": "101", "endLines": "9", "endColumns": "12", "endOffsets": "399"}, "to": {"startLines": "28", "startColumns": "4", "startOffsets": "1525", "endLines": "33", "endColumns": "12", "endOffsets": "1823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "34,35,36,37,38,39,40,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1828,1898,1982,2066,2162,2264,2366,6070", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1893,1977,2061,2157,2259,2361,2455,6154"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,8", "startColumns": "4,4", "startOffsets": "100,365", "endLines": "5,21", "endColumns": "12,12", "endOffsets": "311,1124"}, "to": {"startLines": "24,67", "startColumns": "4,4", "startOffsets": "1413,5348", "endLines": "27,80", "endColumns": "12,12", "endOffsets": "1520,6065"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "54,8,10,30,32,28,14,56,16,12,42,40,22,36,48,46,38,20,52,4,6,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2891,492,599,1649,1758,1546,901,3034,1053,750,2292,2173,1320,1925,2595,2478,2048,1232,2780,203,348,1413", "endColumns": "80,61,63,62,54,51,63,87,65,63,57,57,54,58,67,59,54,50,68,55,57,51", "endOffsets": "2967,549,658,1707,1808,1593,960,3117,1114,809,2345,2226,1370,1979,2658,2533,2098,1278,2844,254,401,1460"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,262,325,380,432,496,584,650,714,772,830,885,944,1012,1072,1127,1178,1247,1303,1361", "endColumns": "80,61,63,62,54,51,63,87,65,63,57,57,54,58,67,59,54,50,68,55,57,51", "endOffsets": "131,193,257,320,375,427,491,579,645,709,767,825,880,939,1007,1067,1122,1173,1242,1298,1356,1408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2460,2535,2646,2735,2836,2943,3050,3149,3256,3359,3486,3574,3698,3800,3902,4018,4120,4234,4362,4478,4600,4736,4856,4990,5110,5222,6159,6276,6400,6530,6652,6790,6924,7040", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2530,2641,2730,2831,2938,3045,3144,3251,3354,3481,3569,3693,3795,3897,4013,4115,4229,4357,4473,4595,4731,4851,4985,5105,5217,5343,6271,6395,6525,6647,6785,6919,7035,7155"}}]}]}