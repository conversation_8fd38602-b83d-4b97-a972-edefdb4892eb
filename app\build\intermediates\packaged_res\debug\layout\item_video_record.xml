<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <!-- 摄像头方向 -->
    <TextView
        android:id="@+id/tv_camera_direction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="前视"
        android:textColor="@color/text_adaptive"
        android:textSize="14sp"
        android:textStyle="bold"
        android:minWidth="40dp"
        android:gravity="center"/>
        <!-- 使用text_adaptive - 指向text_primary_adaptive，日间#000000，夜间#FFFFFF -->

    <!-- 录制时间 -->
    <TextView
        android:id="@+id/tv_record_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:text="2024年12月19日 14:30:25"
        android:textColor="@color/text_secondary_adaptive"
        android:textSize="12sp"/>
        <!-- 使用text_secondary_adaptive - 次要文本颜色，日间#666666，夜间#CCCCCC -->

</LinearLayout>
