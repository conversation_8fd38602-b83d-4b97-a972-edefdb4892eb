<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="incidents">

    <incident
        id="Deprecated"
        severity="warning"
        message="Consider removing `sharedUserId` for new users by adding `android:sharedUserMaxSdkVersion=&quot;32&quot;` to your manifest. See https://developer.android.com/guide/topics/manifest/manifest-element for details.">
        <fix-attribute
            description="Set sharedUserMaxSdkVersion=&quot;32&quot;"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="sharedUserMaxSdkVersion"
            value="32"/>
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="4"
            column="5"
            startOffset="163"
            endLine="4"
            endColumn="46"
            endOffset="204"/>
    </incident>

    <incident
        id="GradleOverrides"
        severity="warning"
        message="This `minSdkVersion` value (`23`) is not used; it is always overridden by the value specified in the Gradle build script (`30`)">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="9"
            column="9"
            startOffset="299"
            endLine="9"
            endColumn="35"
            endOffset="325"/>
    </incident>

    <incident
        id="GradleOverrides"
        severity="warning"
        message="This `targetSdkVersion` value (`33`) is not used; it is always overridden by the value specified in the Gradle build script (`34`)">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="10"
            column="9"
            startOffset="334"
            endLine="10"
            endColumn="38"
            endOffset="363"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldPattern="targetSdkVersion\s*=\s*[&quot;&apos;](.*)[&quot;&apos;]"
            replacement="36"/>
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="10"
            column="9"
            startOffset="334"
            endLine="10"
            endColumn="38"
            endOffset="363"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="14"
            column="22"
            startOffset="479"
            endLine="14"
            endColumn="69"
            endOffset="526"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="15"
            column="22"
            startOffset="551"
            endLine="15"
            endColumn="77"
            endOffset="606"/>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="The Google Play store has a policy that limits usage of MANAGE_EXTERNAL_STORAGE">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="19"
            column="36"
            startOffset="750"
            endLine="19"
            endColumn="78"
            endOffset="792"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="26"
            column="22"
            startOffset="1012"
            endLine="26"
            endColumn="88"
            endOffset="1078"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="36"
            column="22"
            startOffset="1541"
            endLine="36"
            endColumn="82"
            endOffset="1601"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="37"
            column="22"
            startOffset="1625"
            endLine="37"
            endColumn="77"
            endOffset="1680"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.autolink.dvr.p003ui.file.FileActivity`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="92"
            column="27"
            startOffset="3634"
            endLine="92"
            endColumn="68"
            endOffset="3675"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.autolink.dvr.p003ui.VideoActivity`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="96"
            column="27"
            startOffset="3776"
            endLine="96"
            endColumn="64"
            endOffset="3813"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="104"
            column="10"
            startOffset="4086"
            endLine="104"
            endColumn="17"
            endOffset="4093"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.autolink.sbjk.common.service.AidlService`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="105"
            column="27"
            startOffset="4120"
            endLine="105"
            endColumn="54"
            endOffset="4147"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.autolink.sbjk.common.service.DVRService`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="115"
            column="27"
            startOffset="4497"
            endLine="115"
            endColumn="53"
            endOffset="4523"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.autolink.sbjk.common.receiver.BootCompleteReceiver`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="118"
            column="27"
            startOffset="4584"
            endLine="118"
            endColumn="64"
            endOffset="4621"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `androidx.startup.InitializationProvider`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="141"
            column="27"
            startOffset="5486"
            endLine="141"
            endColumn="66"
            endOffset="5525"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:android"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/play_pause_button_state.xml"
            line="4"
            column="17"
            startOffset="170"
            endLine="4"
            endColumn="75"
            endOffset="228"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:android"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/play_pause_button_state.xml"
            line="15"
            column="17"
            startOffset="583"
            endLine="15"
            endColumn="75"
            endOffset="641"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/settings_icon.xml"
            line="9"
            column="27"
            startOffset="301"
            endLine="9"
            endColumn="931"
            endOffset="1205"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;开始录制&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_camera_preview.xml"
            line="18"
            column="9"
            startOffset="645"
            endLine="18"
            endColumn="28"
            endOffset="664"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;哨兵监控&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="42"
            column="17"
            startOffset="1728"
            endLine="42"
            endColumn="36"
            endOffset="1747"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;录像回放&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="58"
            column="17"
            startOffset="2391"
            endLine="58"
            endColumn="36"
            endOffset="2410"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;自动启动哨兵功能&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="97"
            column="25"
            startOffset="3895"
            endLine="97"
            endColumn="48"
            endOffset="3918"/>
    </incident>

    <incident
        id="UseSwitchCompatOrMaterialXml"
        severity="warning"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="103"
            column="21"
            startOffset="4161"
            endLine="107"
            endColumn="50"
            endOffset="4400"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5721"
                    endOffset="9290"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6046"
                    endOffset="6585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6686"
                    endOffset="7294"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="7438"
                    endOffset="8045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8071"
                    endOffset="8678"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8704"
                    endOffset="9253"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="150"
            column="26"
            startOffset="6047"
            endLine="150"
            endColumn="32"
            endOffset="6053"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;全部&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="156"
            column="29"
            startOffset="6368"
            endLine="156"
            endColumn="46"
            endOffset="6385"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5721"
                    endOffset="9290"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6046"
                    endOffset="6585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6686"
                    endOffset="7294"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="7438"
                    endOffset="8045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8071"
                    endOffset="8678"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8704"
                    endOffset="9253"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="162"
            column="26"
            startOffset="6687"
            endLine="162"
            endColumn="32"
            endOffset="6693"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;前视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="169"
            column="29"
            startOffset="7071"
            endLine="169"
            endColumn="46"
            endOffset="7088"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5721"
                    endOffset="9290"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6046"
                    endOffset="6585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6686"
                    endOffset="7294"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="7438"
                    endOffset="8045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8071"
                    endOffset="8678"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8704"
                    endOffset="9253"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="175"
            column="26"
            startOffset="7439"
            endLine="175"
            endColumn="32"
            endOffset="7445"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;后视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="182"
            column="29"
            startOffset="7822"
            endLine="182"
            endColumn="46"
            endOffset="7839"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5721"
                    endOffset="9290"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6046"
                    endOffset="6585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6686"
                    endOffset="7294"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="7438"
                    endOffset="8045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8071"
                    endOffset="8678"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8704"
                    endOffset="9253"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="187"
            column="26"
            startOffset="8072"
            endLine="187"
            endColumn="32"
            endOffset="8078"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;左视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="194"
            column="29"
            startOffset="8455"
            endLine="194"
            endColumn="46"
            endOffset="8472"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5721"
                    endOffset="9290"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6046"
                    endOffset="6585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="6686"
                    endOffset="7294"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="7438"
                    endOffset="8045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8071"
                    endOffset="8678"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="8704"
                    endOffset="9253"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="199"
            column="26"
            startOffset="8705"
            endLine="199"
            endColumn="32"
            endOffset="8711"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;右视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="205"
            column="29"
            startOffset="9030"
            endLine="205"
            endColumn="46"
            endOffset="9047"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="213"
            column="22"
            startOffset="9351"
            endLine="213"
            endColumn="34"
            endOffset="9363"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;全部&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="232"
            column="33"
            startOffset="10276"
            endLine="232"
            endColumn="50"
            endOffset="10293"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;月份&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="251"
            column="33"
            startOffset="11170"
            endLine="251"
            endColumn="50"
            endOffset="11187"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;日期&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="270"
            column="33"
            startOffset="12068"
            endLine="270"
            endColumn="50"
            endOffset="12085"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;小时&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="289"
            column="33"
            startOffset="12967"
            endLine="289"
            endColumn="50"
            endOffset="12984"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024年12月19日 14:30:25&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="356"
            column="13"
            startOffset="15304"
            endLine="356"
            endColumn="48"
            endOffset="15339"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;前视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="403"
            column="21"
            startOffset="17202"
            endLine="403"
            endColumn="38"
            endOffset="17219"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;后视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="443"
            column="21"
            startOffset="18927"
            endLine="443"
            endColumn="38"
            endOffset="18944"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;左视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="483"
            column="21"
            startOffset="20662"
            endLine="483"
            endColumn="38"
            endOffset="20679"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;右视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="523"
            column="21"
            startOffset="22393"
            endLine="523"
            endColumn="38"
            endOffset="22410"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;▶&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="593"
            column="25"
            startOffset="25545"
            endLine="593"
            endColumn="41"
            endOffset="25561"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;1.0x&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="614"
            column="25"
            startOffset="26544"
            endLine="614"
            endColumn="44"
            endOffset="26563"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="628"
            column="25"
            startOffset="27220"
            endLine="628"
            endColumn="45"
            endOffset="27240"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;前视&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item_video_record.xml"
            line="15"
            column="9"
            startOffset="537"
            endLine="15"
            endColumn="26"
            endOffset="554"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024年12月19日 14:30:25&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item_video_record.xml"
            line="30"
            column="9"
            startOffset="1053"
            endLine="30"
            endColumn="44"
            endOffset="1088"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/adapter/VideoListAdapter.java"
            line="71"
            column="9"
            startOffset="2052"
            endLine="71"
            endColumn="31"
            endOffset="2074"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/adapter/VideoListAdapter.java"
            line="81"
            column="9"
            startOffset="2265"
            endLine="81"
            endColumn="31"
            endOffset="2287"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/CameraPreviewActivity.java"
            line="212"
            column="34"
            startOffset="6627"
            endLine="212"
            endColumn="53"
            endOffset="6646"/>
    </incident>

    <incident
        id="ConstantLocale"
        severity="warning"
        message="Assigning `Locale.getDefault()` to a final static field is suspicious; this code will not work correctly if the user changes locale while the app is running">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/encoder/CameraYuvEncoder.java"
            line="167"
            column="97"
            startOffset="6298"
            endLine="167"
            endColumn="116"
            endOffset="6317"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `PlaybackLifecycleManager` which has field `hostActivity` pointing to `Activity`); this is a memory leak">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/lifecycle/PlaybackLifecycleManager.java"
            line="39"
            column="13"
            startOffset="1160"
            endLine="39"
            endColumn="19"
            endOffset="1166"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="352"
            column="42"
            startOffset="11281"
            endLine="359"
            endColumn="10"
            endOffset="11604"/>
    </incident>

    <incident
        id="UseSwitchCompatOrMaterialCode"
        severity="warning"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="94"
            column="5"
            startOffset="3181"
            endLine="94"
            endColumn="37"
            endOffset="3213"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``Button`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="352"
            column="9"
            startOffset="11248"
            endLine="359"
            endColumn="11"
            endOffset="11605"/>
    </incident>

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Overriding method should call `super.onBackPressed`">
        <fix-data/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="845"
            column="17"
            startOffset="26959"
            endLine="845"
            endColumn="30"
            endOffset="26972"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="1260"
            column="21"
            startOffset="41349"
            endLine="1260"
            endColumn="51"
            endOffset="41379"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="1328"
            column="21"
            startOffset="43443"
            endLine="1328"
            endColumn="51"
            endOffset="43473"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="1793"
            column="16"
            startOffset="58779"
            endLine="1793"
            endColumn="60"
            endOffset="58823"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="1894"
            column="47"
            startOffset="62527"
            endLine="1894"
            endColumn="54"
            endOffset="62534"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/entity/VideoSegment.java"
            line="148"
            column="20"
            startOffset="4095"
            endLine="148"
            endColumn="63"
            endOffset="4138"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/entity/VideoSegment.java"
            line="150"
            column="20"
            startOffset="4211"
            endLine="150"
            endColumn="74"
            endOffset="4265"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/entity/VideoSegment.java"
            line="152"
            column="20"
            startOffset="4303"
            endLine="152"
            endColumn="83"
            endOffset="4366"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/entity/VideoSegment.java"
            line="166"
            column="20"
            startOffset="4720"
            endLine="166"
            endColumn="76"
            endOffset="4776"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/entity/VideoSegment.java"
            line="168"
            column="20"
            startOffset="4814"
            endLine="168"
            endColumn="64"
            endOffset="4858"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/VideoRecordInfo.java"
            line="127"
            column="20"
            startOffset="3844"
            endLine="127"
            endColumn="63"
            endOffset="3887"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/VideoRecordInfo.java"
            line="129"
            column="20"
            startOffset="3960"
            endLine="129"
            endColumn="74"
            endOffset="4014"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/model/VideoRecordInfo.java"
            line="131"
            column="20"
            startOffset="4052"
            endLine="131"
            endColumn="83"
            endOffset="4115"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="132"
            column="41"
            startOffset="3942"
            endLine="132"
            endColumn="52"
            endOffset="3953"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="241"
            column="41"
            startOffset="7311"
            endLine="241"
            endColumn="52"
            endOffset="7322"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/PlaybackSpeedManager.java"
            line="113"
            column="17"
            startOffset="3016"
            endLine="113"
            endColumn="63"
            endOffset="3062"/>
    </incident>

    <incident
        id="DiscouragedPrivateApi"
        severity="warning"
        message="Reflective access to mMediaPlayer, which is not part of the public SDK and therefore likely to change in future Android releases">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/PlaybackSpeedManager.java"
            line="115"
            column="49"
            startOffset="3153"
            endLine="115"
            endColumn="97"
            endOffset="3201"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/PlaybackSpeedManager.java"
            line="205"
            column="16"
            startOffset="5744"
            endLine="205"
            endColumn="62"
            endOffset="5790"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/TimePickerManager.java"
            line="145"
            column="37"
            startOffset="4427"
            endLine="145"
            endColumn="59"
            endOffset="4449"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/TimePickerManager.java"
            line="221"
            column="36"
            startOffset="7375"
            endLine="221"
            endColumn="64"
            endOffset="7403"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/TimePickerManager.java"
            line="322"
            column="35"
            startOffset="11038"
            endLine="322"
            endColumn="57"
            endOffset="11060"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/TimePickerManager.java"
            line="479"
            column="36"
            startOffset="16558"
            endLine="479"
            endColumn="55"
            endOffset="16577"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/TimePickerManager.java"
            line="486"
            column="38"
            startOffset="16767"
            endLine="486"
            endColumn="55"
            endOffset="16784"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/TimePickerManager.java"
            line="494"
            column="39"
            startOffset="16992"
            endLine="494"
            endColumn="75"
            endOffset="17028"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/TimePickerManager.java"
            line="576"
            column="16"
            startOffset="18866"
            endLine="576"
            endColumn="84"
            endOffset="18934"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/widget/RecordingIndicator.java"
            line="218"
            column="20"
            startOffset="6686"
            endLine="218"
            endColumn="76"
            endOffset="6742"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/ui/widget/RecordingIndicator.java"
            line="220"
            column="20"
            startOffset="6780"
            endLine="220"
            endColumn="64"
            endOffset="6824"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `VehicleManager` which has field `mContext` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/vehicle/VehicleManager.java"
            line="39"
            column="13"
            startOffset="1308"
            endLine="39"
            endColumn="19"
            endOffset="1314"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/viewmodel/CameraPreviewViewModel.java"
            line="244"
            column="16"
            startOffset="7934"
            endLine="244"
            endColumn="72"
            endOffset="7990"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/viewmodel/MainViewModel.java"
            line="276"
            column="16"
            startOffset="8503"
            endLine="276"
            endColumn="72"
            endOffset="8559"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="34"
            replacement="36"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="12"
            column="9"
            startOffset="197"
            endLine="12"
            endColumn="21"
            endOffset="209"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.7.0"
            replacement="1.7.1"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="38"
            column="20"
            startOffset="769"
            endLine="38"
            endColumn="56"
            endOffset="805"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.camera:camera-core than 1.3.2 is available: 1.4.2">
        <fix-replace
            description="Change to 1.4.2"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.2"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="44"
            column="20"
            startOffset="1038"
            endLine="44"
            endColumn="55"
            endOffset="1073"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.camera:camera-camera2 than 1.3.2 is available: 1.4.2">
        <fix-replace
            description="Change to 1.4.2"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.2"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="45"
            column="20"
            startOffset="1093"
            endLine="45"
            endColumn="58"
            endOffset="1131"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.camera:camera-lifecycle than 1.3.2 is available: 1.4.2">
        <fix-replace
            description="Change to 1.4.2"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.2"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="46"
            column="20"
            startOffset="1151"
            endLine="46"
            endColumn="60"
            endOffset="1191"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.camera:camera-view than 1.3.2 is available: 1.4.2">
        <fix-replace
            description="Change to 1.4.2"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.2"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="47"
            column="20"
            startOffset="1211"
            endLine="47"
            endColumn="55"
            endOffset="1246"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.2.1 is available: 1.3.0">
        <fix-replace
            description="Change to 1.3.0"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.3.0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="54"
            column="31"
            startOffset="1402"
            endLine="54"
            endColumn="62"
            endOffset="1433"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.6.1 is available: 3.7.0">
        <fix-replace
            description="Change to 3.7.0"
            family="Update versions"
            oldString="3.6.1"
            replacement="3.7.0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="55"
            column="31"
            startOffset="1464"
            endLine="55"
            endColumn="75"
            endOffset="1508"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary_adaptive` with a theme that also paints a background (inferred theme is `@style/Theme.Sbjk`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="8"
            column="5"
            startOffset="345"
            endLine="8"
            endColumn="60"
            endOffset="400"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.Sbjk`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item_video_record.xml"
            line="7"
            column="5"
            startOffset="261"
            endLine="7"
            endColumn="64"
            endOffset="320"/>
    </incident>

</incidents>
