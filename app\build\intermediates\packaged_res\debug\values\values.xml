<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="background_light">#DEE2E5</color>
    <color name="background_primary_adaptive">#DEE2E5</color>
    <color name="background_secondary_adaptive">#DEE2E5</color>
    <color name="button_background_selected">#44676767</color>
    <color name="button_outline_color">@color/colorPrimary</color>
    <color name="button_text_selected_adaptive">#000000</color>
    <color name="button_text_unselected">@color/button_text_unselected_adaptive</color>
    <color name="button_text_unselected_adaptive">#808080</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="container_background_adaptive">#F5F5F5</color>
    <color name="dialog_background_color">#CED0D1</color>
    <color name="dialog_label_text_color">#9C9C9C</color>
    <color name="number_picker_text_color">#000000</color>
    <color name="player_control_text_color">#FFFFFF</color>
    <color name="separator_line_color">#9C9C9C</color>
    <color name="status_bar_color">#DEE2E5</color>
    <color name="text_adaptive">@color/text_primary_adaptive</color>
    <color name="text_primary_adaptive">#000000</color>
    <color name="text_secondary_adaptive">#666666</color>
    <color name="white">#FFFFFF</color>
    <string name="app_name">哨兵监控</string>
    <style name="CustomButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
        <item name="android:textColor">@color/colorPrimary</item>  
        <item name="android:drawableTint">@color/white</item>     
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="NumberPickerStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/number_picker_text_color</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="Theme.Sbjk" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        
        <item name="colorAccent">@color/colorAccent</item>
        
        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:navigationBarColor">@color/colorPrimaryDark</item>
        <item name="android:statusBarColor">@color/background_light</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">true</item>
        <item name="android:windowBackground">@color/background_light</item>
    </style>
</resources>