<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="background_adaptive">@color/background_primary_adaptive</color>
    <color name="background_light">#DEE2E5</color>
    <color name="background_primary_adaptive">#DEE2E5</color>
    <color name="background_secondary_adaptive">#DEE2E5</color>
    <color name="black">#000000</color>
    <color name="button_background_selected">#44676767</color>
    <color name="button_normal_background_color">#F0F0F0</color>
    <color name="button_outline_color">@color/colorPrimary</color>
    <color name="button_pressed_background_color">#E0E0E0</color>
    <color name="button_text_color">@color/colorPrimary</color>
    <color name="button_text_selected_adaptive">#000000</color>
    <color name="button_text_unselected">@color/button_text_unselected_adaptive</color>
    <color name="button_text_unselected_adaptive">#808080</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="container_background_adaptive">#F5F5F5</color>
    <color name="dialog_background_color">#CED0D1</color>
    <color name="dialog_button_text_color">#000000</color>
    <color name="dialog_label_text_color">#9C9C9C</color>
    <color name="grey">#F5F5F5</color>
    <color name="navigation_bar_color">@color/colorPrimaryDark</color>
    <color name="number_picker_text_color">#000000</color>
    <color name="player_control_background_color">#CC000000</color>
    <color name="player_control_text_color">#FFFFFF</color>
    <color name="separator_line_color">#9C9C9C</color>
    <color name="status_bar_color">#DEE2E5</color>
    <color name="text_adaptive">@color/text_primary_adaptive</color>
    <color name="text_primary">#333333</color>
    <color name="text_primary_adaptive">#000000</color>
    <color name="text_secondary_adaptive">#666666</color>
    <color name="white">#FFFFFF</color>
    <color name="window_background">#DEE2E5</color>
    <dimen name="card_corner_radius">8dp</dimen>
    <string name="app_name">哨兵监控</string>
    <string name="bitrate_selection">比特率:</string>
    <string name="camera_format">相机格式:</string>
    <string name="camera_selection">摄像头:</string>
    <string name="codec_selection">编码器:</string>
    <string name="resolution_selection">分辨率:</string>
    <string name="start_test">开始测试</string>
    <string name="stop_test">停止测试</string>
    <string name="test1_title">摄像头直接编码测试</string>
    <string name="test2_title">文件转码测试</string>
    <string name="test3_title">视频播放同时录制</string>
    <string name="test4_title">相机格式测试</string>
    <string name="test_preparation">选择测试项目开始测试</string>
    <style name="Base.Theme.Sbjk" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="CustomButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
        <item name="android:textColor">@color/colorPrimary</item>  
        <item name="android:drawableTint">@color/white</item>     
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="CustomTextView">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>  
    </style>
    <style name="DialogButtonStyle">
        <item name="android:textColor">@color/dialog_button_text_color</item>
        <item name="android:background">@drawable/dialog_button_background</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:gravity">center</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="NumberPickerStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/number_picker_text_color</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="Theme.Sbjk" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        
        <item name="colorAccent">@color/colorAccent</item>
        
        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:navigationBarColor">@color/colorPrimaryDark</item>
        <item name="android:statusBarColor">@color/background_light</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">true</item>
        <item name="android:windowBackground">@color/background_light</item>
    </style>
    <style name="TimePickerNumberPickerStyle">
        <item name="android:textSize">23sp</item>
        <item name="android:textColor">@color/number_picker_text_color</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:gravity">center</item>
        <item name="android:descendantFocusability">blocksDescendants</item>
        <item name="android:orientation">vertical</item>
    </style>
</resources>