{"logs": [{"outputFile": "com.autolink.sbjk.app-mergeDebugResources-35:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,4542,4604,4669,4764,4845,4908,4997,5061,5130,5193,5267,5331,5388,5506,5564,5626,5683,5763,5902,5991,6067,6162,6243,6325,6466,6547,6627,6778,6868,6948,7004,7060,7126,7205,7287,7358,7447,7521,7598,7668,7747,7847,7931,8015,8107,8207,8281,8362,8464,8517,8602,8669,8762,8851,8913,8977,9040,9108,9221,9328,9432,9533,9593,9653,9819,9902,9978", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,4599,4664,4759,4840,4903,4992,5056,5125,5188,5262,5326,5383,5501,5559,5621,5678,5758,5897,5986,6062,6157,6238,6320,6461,6542,6622,6773,6863,6943,6999,7055,7121,7200,7282,7353,7442,7516,7593,7663,7742,7842,7926,8010,8102,8202,8276,8357,8459,8512,8597,8664,8757,8846,8908,8972,9035,9103,9216,9323,9427,9528,9588,9648,9731,9897,9973,10050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,9736", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,9814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\69992366064d5c049095b4824cf18965\\transformed\\core-1.13.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,10055", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,10151"}}]}]}