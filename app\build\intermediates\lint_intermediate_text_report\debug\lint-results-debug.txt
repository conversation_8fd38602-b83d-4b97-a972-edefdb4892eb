E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\encoder\CameraYuvEncoder.java:544: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
            cameraManager.openCamera(cameraId, new CameraDevice.StateCallback() {
            ^

   Explanation for issues of type "MissingPermission":
   This check scans through your code and libraries and looks at the APIs
   being used, and checks this against the set of permissions required to
   access those APIs. If the code using those APIs is called at runtime, then
   the program will crash.

   Furthermore, for permissions that are revocable (with targetSdkVersion 23),
   client code must also be prepared to handle the calls throwing an exception
   if the user rejects the request for permission at runtime.

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:845: Error: Overriding method should call super.onBackPressed [MissingSuperCall]
    public void onBackPressed() {
                ~~~~~~~~~~~~~

   Explanation for issues of type "MissingSuperCall":
   Some methods, such as View#onDetachedFromWindow, require that you also call
   the super implementation as part of your method.

E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:92: Error: Class referenced in the manifest, com.autolink.dvr.p003ui.file.FileActivity, was not found in the project or the libraries [MissingClass]
            android:name="com.autolink.dvr.p003ui.file.FileActivity"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:96: Error: Class referenced in the manifest, com.autolink.dvr.p003ui.VideoActivity, was not found in the project or the libraries [MissingClass]
            android:name="com.autolink.dvr.p003ui.VideoActivity"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:105: Error: Class referenced in the manifest, com.autolink.sbjk.common.service.AidlService, was not found in the project or the libraries [MissingClass]
            android:name=".common.service.AidlService"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:115: Error: Class referenced in the manifest, com.autolink.sbjk.common.service.DVRService, was not found in the project or the libraries [MissingClass]
            android:name=".common.service.DVRService"/>
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:118: Error: Class referenced in the manifest, com.autolink.sbjk.common.receiver.BootCompleteReceiver, was not found in the project or the libraries [MissingClass]
            android:name=".common.receiver.BootCompleteReceiver"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:141: Error: Class referenced in the manifest, androidx.startup.InitializationProvider, was not found in the project or the libraries [MissingClass]
            android:name="androidx.startup.InitializationProvider"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingClass":
   If a class is referenced in the manifest or in a layout file, it must also
   exist in the project (or in one of the libraries included by the project.
   This check helps uncover typos in registration names, or attempts to rename
   or move classes without updating the XML references properly.

   https://developer.android.com/guide/topics/manifest/manifest-intro.html

E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:19: Warning: The Google Play store has a policy that limits usage of MANAGE_EXTERNAL_STORAGE [ScopedStorage]
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\viewmodel\CameraPreviewViewModel.java:244: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:1793: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%02d:%02d", minutes, seconds);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\viewmodel\MainViewModel.java:276: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\scanner\QuickVideoScanner.java:132: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                String lowerName = name.toLowerCase();
                                        ~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\scanner\QuickVideoScanner.java:241: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                String lowerName = name.toLowerCase();
                                        ~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\widget\RecordingIndicator.java:218: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\widget\RecordingIndicator.java:220: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%02d:%02d", minutes, seconds);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java:145: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            monthDisplayValues[i] = String.format("%d", i);
                                    ~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java:221: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            hourDisplayValues[i] = String.format("%02d", i - 1);
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java:322: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            dayDisplayValues[i] = String.format("%d", i);
                                  ~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java:494: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                btnHourPicker.setText(String.format("%02d时", selectedHour));
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java:576: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%d月%d日%d时", selectedMonth, selectedDay, selectedHour);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\VideoRecordInfo.java:127: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f KB", fileSize / 1024.0);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\VideoRecordInfo.java:129: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\VideoRecordInfo.java:131: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java:148: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f KB", fileSize / 1024.0);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java:150: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java:152: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java:166: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\model\entity\VideoSegment.java:168: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%02d:%02d", minutes, seconds);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.US) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\PlaybackSpeedManager.java:115: Warning: Reflective access to mMediaPlayer, which is not part of the public SDK and therefore likely to change in future Android releases [DiscouragedPrivateApi]
                java.lang.reflect.Field field = VideoView.class.getDeclaredField("mMediaPlayer");
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedPrivateApi":
   Usage of restricted non-SDK interface may throw an exception at runtime.
   Accessing non-SDK methods or fields through reflection has a high
   likelihood to break your app between versions, and is being restricted to
   facilitate future app compatibility.

   https://developer.android.com/preview/restrictions-non-sdk-interfaces

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\service\CameraService.java:251: Error: When targeting Android 13 or higher, posting a permission requires holding the POST_NOTIFICATIONS permission [NotificationPermission]
            manager.notify(NOTIFICATION_ID, notification);
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotificationPermission":
   When targeting Android 13 and higher, posting permissions requires holding
   the runtime permission android.permission.POST_NOTIFICATIONS.

E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:10: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        android:targetSdkVersion="33"/>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\build.gradle:12: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:14: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.SYSTEM_CAMERA" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:15: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS"/>
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:26: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.START_ACTIVITIES_FROM_BACKGROUND"/>
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:36: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL"/>
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:37: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS"/>
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ProtectedPermissions":
   Permissions with the protection level signature, privileged or
   signatureOrSystem are only granted to system apps. If an app is a regular
   non-system app, it will never be able to use these permissions.

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:94: Warning: Use SwitchCompat from AppCompat or MaterialSwitch from Material library [UseSwitchCompatOrMaterialCode from androidx.appcompat]
    private Switch switchSentryAuto;
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseSwitchCompatOrMaterialCode":
   Use SwitchCompat from AppCompat or MaterialSwitch from Material library

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:103: Warning: Use SwitchCompat from AppCompat or MaterialSwitch from Material library [UseSwitchCompatOrMaterialXml from androidx.appcompat]
                    <Switch
                    ^

   Explanation for issues of type "UseSwitchCompatOrMaterialXml":
   Use SwitchCompat from AppCompat or MaterialSwitch from Material library

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

E:\XM\rsbjk-a\app\build.gradle:38: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.appcompat:appcompat:1.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\build.gradle:44: Warning: A newer version of androidx.camera:camera-core than 1.3.2 is available: 1.4.2 [GradleDependency]
    implementation 'androidx.camera:camera-core:1.3.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\build.gradle:45: Warning: A newer version of androidx.camera:camera-camera2 than 1.3.2 is available: 1.4.2 [GradleDependency]
    implementation 'androidx.camera:camera-camera2:1.3.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\build.gradle:46: Warning: A newer version of androidx.camera:camera-lifecycle than 1.3.2 is available: 1.4.2 [GradleDependency]
    implementation 'androidx.camera:camera-lifecycle:1.3.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\build.gradle:47: Warning: A newer version of androidx.camera:camera-view than 1.3.2 is available: 1.4.2 [GradleDependency]
    implementation 'androidx.camera:camera-view:1.3.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\build.gradle:54: Warning: A newer version of androidx.test.ext:junit than 1.2.1 is available: 1.3.0 [GradleDependency]
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\build.gradle:55: Warning: A newer version of androidx.test.espresso:espresso-core than 3.6.1 is available: 3.7.0 [GradleDependency]
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:9: Warning: This minSdkVersion value (23) is not used; it is always overridden by the value specified in the Gradle build script (30) [GradleOverrides]
        android:minSdkVersion="23"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:10: Warning: This targetSdkVersion value (33) is not used; it is always overridden by the value specified in the Gradle build script (34) [GradleOverrides]
        android:targetSdkVersion="33"/>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleOverrides":
   The value of (for example) minSdkVersion is only used if it is not
   specified in the build.gradle build scripts. When specified in the Gradle
   build scripts, the manifest value is ignored and can be misleading, so
   should be removed to avoid ambiguity.

E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:4: Warning: Consider removing sharedUserId for new users by adding android:sharedUserMaxSdkVersion="32" to your manifest. See https://developer.android.com/guide/topics/manifest/manifest-element for details. [Deprecated]
    android:sharedUserId="android.uid.system"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Deprecated":
   Deprecated views, attributes and so on are deprecated because there is a
   better way to do something. Do it that new way. You've been warned.

E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:104: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~

   Explanation for issues of type "ExportedService":
   Exported services (services which either set exported=true or contain an
   intent-filter and do not specify exported=false) should define a permission
   that an entity must have in order to launch the service or bind to it.
   Without this, any application can use this service.

   https://goo.gle/ExportedService

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:1260: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                    adapter.notifyDataSetChanged();
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:1328: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                    adapter.notifyDataSetChanged();
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\adapter\VideoListAdapter.java:71: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\adapter\VideoListAdapter.java:81: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\PlaybackSpeedManager.java:113: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\PlaybackSpeedManager.java:205: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M;
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\values\themes.xml:17: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
                                                  ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\lifecycle\PlaybackLifecycleManager.java:39: Warning: Do not place Android context classes in static fields (static reference to PlaybackLifecycleManager which has field hostActivity pointing to Activity); this is a memory leak [StaticFieldLeak]
    private static volatile PlaybackLifecycleManager instance;
            ~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\vehicle\VehicleManager.java:39: Warning: Do not place Android context classes in static fields (static reference to VehicleManager which has field mContext pointing to Context); this is a memory leak [StaticFieldLeak]
    private static VehicleManager sInstance;
            ~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

E:\XM\rsbjk-a\app\src\main\res\drawable\settings_icon.xml:9: Warning: Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z"/>
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:213: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
                    <LinearLayout
                     ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:8: Warning: Possible overdraw: Root element paints background @color/background_primary_adaptive with a theme that also paints a background (inferred theme is @style/Theme.Sbjk) [Overdraw]
    android:background="@color/background_primary_adaptive"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\item_video_record.xml:7: Warning: Possible overdraw: Root element paints background ?android:attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/Theme.Sbjk) [Overdraw]
    android:background="?android:attr/selectableItemBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

E:\XM\rsbjk-a\app\src\main\res\drawable\play_pause_button_state.xml:4: Warning: This namespace declaration is redundant [RedundantNamespace]
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\drawable\play_pause_button_state.xml:15: Warning: This namespace declaration is redundant [RedundantNamespace]
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantNamespace":
   In Android XML documents, only specify the namespace on the root/document
   element. Namespace declarations elsewhere in the document are typically
   accidental leftovers from copy/pasting XML from other files or
   documentation.

E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:150: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:162: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:175: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:187: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:199: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://material.io/components/dialogs/

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:352: Warning: Custom view `Button` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        btnAllCameras.setOnTouchListener((v, event) -> {
        ^
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:352: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
        btnAllCameras.setOnTouchListener((v, event) -> {
                                         ^

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\encoder\CameraYuvEncoder.java:167: Warning: Assigning Locale.getDefault() to a final static field is suspicious; this code will not work correctly if the user changes locale while the app is running [ConstantLocale]
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
                                                                                                ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ConstantLocale":
   Assigning Locale.getDefault() to a constant is suspicious, because the
   locale can change while the app is running.

E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\CameraPreviewActivity.java:212: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            tvCameraInfo.setText("相机: " + cameraName);
                                 ~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\MainActivity.java:1894: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        tvCurrentTime.setText("00:00");
                                              ~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java:479: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            btnMonthPicker.setText(selectedMonth + "月");
                                   ~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\java\com\autolink\sbjk\ui\TimePickerManager.java:486: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                btnDayPicker.setText(selectedDay + "日");
                                     ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

E:\XM\rsbjk-a\app\src\main\res\layout\activity_camera_preview.xml:18: Warning: Hardcoded string "开始录制", should use @string resource [HardcodedText]
        android:text="开始录制"
        ~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:42: Warning: Hardcoded string "哨兵监控", should use @string resource [HardcodedText]
                android:text="哨兵监控"
                ~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:58: Warning: Hardcoded string "录像回放", should use @string resource [HardcodedText]
                android:text="录像回放"
                ~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:97: Warning: Hardcoded string "自动启动哨兵功能", should use @string resource [HardcodedText]
                        android:text="自动启动哨兵功能"
                        ~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:156: Warning: Hardcoded string "全部", should use @string resource [HardcodedText]
                            android:text="全部"
                            ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:169: Warning: Hardcoded string "前视", should use @string resource [HardcodedText]
                            android:text="前视"
                            ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:182: Warning: Hardcoded string "后视", should use @string resource [HardcodedText]
                            android:text="后视"
                            ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:194: Warning: Hardcoded string "左视", should use @string resource [HardcodedText]
                            android:text="左视"
                            ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:205: Warning: Hardcoded string "右视", should use @string resource [HardcodedText]
                            android:text="右视"
                            ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:232: Warning: Hardcoded string "全部", should use @string resource [HardcodedText]
                                android:text="全部"
                                ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:251: Warning: Hardcoded string "月份", should use @string resource [HardcodedText]
                                android:text="月份"
                                ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:270: Warning: Hardcoded string "日期", should use @string resource [HardcodedText]
                                android:text="日期"
                                ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:289: Warning: Hardcoded string "小时", should use @string resource [HardcodedText]
                                android:text="小时"
                                ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:356: Warning: Hardcoded string "2024年12月19日 14:30:25", should use @string resource [HardcodedText]
            android:text="2024年12月19日 14:30:25"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:403: Warning: Hardcoded string "前视", should use @string resource [HardcodedText]
                    android:text="前视"
                    ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:443: Warning: Hardcoded string "后视", should use @string resource [HardcodedText]
                    android:text="后视"
                    ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:483: Warning: Hardcoded string "左视", should use @string resource [HardcodedText]
                    android:text="左视"
                    ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:523: Warning: Hardcoded string "右视", should use @string resource [HardcodedText]
                    android:text="右视"
                    ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:593: Warning: Hardcoded string "▶", should use @string resource [HardcodedText]
                        android:text="▶"
                        ~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:614: Warning: Hardcoded string "1.0x", should use @string resource [HardcodedText]
                        android:text="1.0x"
                        ~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\activity_main.xml:628: Warning: Hardcoded string "00:00", should use @string resource [HardcodedText]
                        android:text="00:00"
                        ~~~~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\item_video_record.xml:15: Warning: Hardcoded string "前视", should use @string resource [HardcodedText]
        android:text="前视"
        ~~~~~~~~~~~~~~~~~
E:\XM\rsbjk-a\app\src\main\res\layout\item_video_record.xml:30: Warning: Hardcoded string "2024年12月19日 14:30:25", should use @string resource [HardcodedText]
        android:text="2024年12月19日 14:30:25"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

14 errors, 87 warnings
