<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.autolink.sbjk.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.autolink.sbjk.common.receiver.BootCompleteReceiver"
                    boolean="true"/>
                <entry
                    name="com.autolink.sbjk.common.service.AidlService"
                    boolean="true"/>
            </map>
    </map>
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnusedResources">
        <entry
            name="model"
            string="color[colorPrimary(U),colorPrimaryDark(U),dialog_label_text_color(U),background_light(U),background_primary_adaptive(U),button_text_unselected_adaptive(U),text_secondary_adaptive(U),button_text_unselected(U),colorAccent(U),background_secondary_adaptive(U),button_background_selected(U),white(U),text_adaptive(U),container_background_adaptive(U),number_picker_text_color(U),separator_line_color(U),text_primary_adaptive(U),player_control_text_color(U),button_outline_color(U),status_bar_color(U),button_text_selected_adaptive(U),dialog_background_color(U)],drawable[button_background(U),play_pause_button_state(U),settings_icon(U),ic_launcher_foreground(U),ic_launcher_background(U),ic_launcher_foreground_1(R),button_outline(U),text_stroke_background(U)],id[video_playback_page(U),video_player_container(D),left_camera_container(U),live_monitor_page(U),back_camera_container(U),left_camera_view(U),btn_playback_speed(U),btn_filter_back(U),camera_surface_view(U),tv_datetime_display(U),back_camera_view(U),tv_camera_direction(U),video_progress_bar(U),video_player_card(D),recycler_video_list(U),main_video_player(U),content_container(D),btn_sentinel_monitor(U),right_camera_container(U),btn_day_picker(U),front_camera_container(U),btn_video_playback(U),btn_filter_left(U),tv_current_time(U),player_controls_overlay(D),right_content_container(D),tv_record_time(U),btn_all_cameras(U),btn_filter_front(U),btn_filter_right(U),video_player_page(U),btn_time_filter_all(U),btn_hour_picker(U),btnStartStop(U),bottom_control_bar(D),btn_filter_all(U),front_camera_view(U),right_preview_area(U),sentinel_monitor_page(U),page_selector_container(D),btn_month_picker(U),btn_play_pause(U),left_control_panel(U),switch_sentry_auto(U),btn_settings(D),right_camera_view(U)],layout[activity_main(U),activity_camera_preview(U),item_video_record(U)],mipmap[ic_launcher_round(U),ic_launcher(U)],string[app_name(U)],style[NumberPickerStyle(U),Theme_AppCompat_Light_NoActionBar(R),Widget_AppCompat_Button_Borderless(R),Theme_AppCompat_DayNight_NoActionBar(R),CustomButton(U),Theme_Sbjk(U)],xml[data_extraction_rules(U),backup_rules(U)];7^5,c^10,12^0,16^a,19^1b,1c^12,4c^4^43^7^b^16^17^56^c^1c^18^48^20^22^1d^30^32,4e^c^6,4f^1a^19,50^1a^19,52^e,56^54^0^b,57^55^0^1^8^3;;;"/>
    </map>

</incidents>
