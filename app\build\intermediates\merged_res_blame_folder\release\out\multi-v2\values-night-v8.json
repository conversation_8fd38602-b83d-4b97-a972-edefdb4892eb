{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-mergeReleaseResources-35:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\picker_styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "29", "startColumns": "4", "startOffsets": "1549", "endLines": "34", "endColumns": "12", "endOffsets": "1847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "35,36,37,38,39,40,41,82", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1852,1922,2006,2090,2186,2288,2390,6103", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1917,2001,2085,2181,2283,2385,2479,6187"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "25,68", "startColumns": "4,4", "startOffsets": "1437,5372", "endLines": "28,81", "endColumns": "12,12", "endOffsets": "1544,6098"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,43,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2360,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2413,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,160,222,286,349,404,456,520,608,674,738,796,854,909,968,1036,1096,1151,1202,1271,1327,1385", "endColumns": "53,50,61,63,62,54,51,63,87,65,63,57,57,54,58,67,59,54,50,68,55,57,51", "endOffsets": "104,155,217,281,344,399,451,515,603,669,733,791,849,904,963,1031,1091,1146,1197,1266,1322,1380,1432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2484,2559,2670,2759,2860,2967,3074,3173,3280,3383,3510,3598,3722,3824,3926,4042,4144,4258,4386,4502,4624,4760,4880,5014,5134,5246,6192,6309,6433,6563,6685,6823,6957,7073", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2554,2665,2754,2855,2962,3069,3168,3275,3378,3505,3593,3717,3819,3921,4037,4139,4253,4381,4497,4619,4755,4875,5009,5129,5241,5367,6304,6428,6558,6680,6818,6952,7068,7188"}}]}, {"outputFile": "com.autolink.sbjk.app-mergeReleaseResources-35:/values-night-v8/values-night-v8.xml", "map": [{"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\picker_styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "29", "startColumns": "4", "startOffsets": "1549", "endLines": "34", "endColumns": "12", "endOffsets": "1847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "35,36,37,38,39,40,41,82", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1852,1922,2006,2090,2186,2288,2390,6103", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1917,2001,2085,2181,2283,2385,2479,6187"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "25,68", "startColumns": "4,4", "startOffsets": "1437,5372", "endLines": "28,81", "endColumns": "12,12", "endOffsets": "1544,6098"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,41,-1,-1,-1,-1,39,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,4,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2241,-1,-1,-1,-1,2116,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,54,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2294,-1,-1,-1,-1,2166,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,160,222,286,349,404,456,520,608,674,738,796,854,909,968,1036,1096,1151,1202,1271,1327,1385", "endColumns": "53,50,61,63,62,54,51,63,87,65,63,57,57,54,58,67,59,54,50,68,55,57,51", "endOffsets": "104,155,217,281,344,399,451,515,603,669,733,791,849,904,963,1031,1091,1146,1197,1266,1322,1380,1432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2484,2559,2670,2759,2860,2967,3074,3173,3280,3383,3510,3598,3722,3824,3926,4042,4144,4258,4386,4502,4624,4760,4880,5014,5134,5246,6192,6309,6433,6563,6685,6823,6957,7073", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2554,2665,2754,2855,2962,3069,3168,3275,3378,3505,3593,3717,3819,3921,4037,4139,4253,4381,4497,4619,4755,4875,5009,5129,5241,5367,6304,6428,6558,6680,6818,6952,7068,7188"}}]}]}