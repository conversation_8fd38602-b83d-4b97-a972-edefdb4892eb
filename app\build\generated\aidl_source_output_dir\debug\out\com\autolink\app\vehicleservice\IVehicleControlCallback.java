/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.autolink.app.vehicleservice;
public interface IVehicleControlCallback extends android.os.IInterface
{
  /** Default implementation for IVehicleControlCallback. */
  public static class Default implements com.autolink.app.vehicleservice.IVehicleControlCallback
  {
    @Override public void onReceiveInt(int propKey, int value, boolean selfGet) throws android.os.RemoteException
    {
    }
    @Override public void onReceiveFloat(int propKey, float value, boolean selfGet) throws android.os.RemoteException
    {
    }
    @Override public void onOnlySyncIntData(int propKey, int value) throws android.os.RemoteException
    {
    }
    @Override public void onOnlySyncFloatData(int propKey, float value) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.autolink.app.vehicleservice.IVehicleControlCallback
  {
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.autolink.app.vehicleservice.IVehicleControlCallback interface,
     * generating a proxy if needed.
     */
    public static com.autolink.app.vehicleservice.IVehicleControlCallback asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.autolink.app.vehicleservice.IVehicleControlCallback))) {
        return ((com.autolink.app.vehicleservice.IVehicleControlCallback)iin);
      }
      return new com.autolink.app.vehicleservice.IVehicleControlCallback.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
      }
      switch (code)
      {
        case TRANSACTION_onReceiveInt:
        {
          int _arg0;
          _arg0 = data.readInt();
          int _arg1;
          _arg1 = data.readInt();
          boolean _arg2;
          _arg2 = (0!=data.readInt());
          this.onReceiveInt(_arg0, _arg1, _arg2);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_onReceiveFloat:
        {
          int _arg0;
          _arg0 = data.readInt();
          float _arg1;
          _arg1 = data.readFloat();
          boolean _arg2;
          _arg2 = (0!=data.readInt());
          this.onReceiveFloat(_arg0, _arg1, _arg2);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_onOnlySyncIntData:
        {
          int _arg0;
          _arg0 = data.readInt();
          int _arg1;
          _arg1 = data.readInt();
          this.onOnlySyncIntData(_arg0, _arg1);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_onOnlySyncFloatData:
        {
          int _arg0;
          _arg0 = data.readInt();
          float _arg1;
          _arg1 = data.readFloat();
          this.onOnlySyncFloatData(_arg0, _arg1);
          reply.writeNoException();
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements com.autolink.app.vehicleservice.IVehicleControlCallback
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void onReceiveInt(int propKey, int value, boolean selfGet) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          _data.writeInt(value);
          _data.writeInt(((selfGet)?(1):(0)));
          boolean _status = mRemote.transact(Stub.TRANSACTION_onReceiveInt, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onReceiveFloat(int propKey, float value, boolean selfGet) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          _data.writeFloat(value);
          _data.writeInt(((selfGet)?(1):(0)));
          boolean _status = mRemote.transact(Stub.TRANSACTION_onReceiveFloat, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onOnlySyncIntData(int propKey, int value) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          _data.writeInt(value);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onOnlySyncIntData, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onOnlySyncFloatData(int propKey, float value) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(propKey);
          _data.writeFloat(value);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onOnlySyncFloatData, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
    }
    static final int TRANSACTION_onReceiveInt = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_onReceiveFloat = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_onOnlySyncIntData = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_onOnlySyncFloatData = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
  }
  public static final java.lang.String DESCRIPTOR = "com.autolink.app.vehicleservice.IVehicleControlCallback";
  public void onReceiveInt(int propKey, int value, boolean selfGet) throws android.os.RemoteException;
  public void onReceiveFloat(int propKey, float value, boolean selfGet) throws android.os.RemoteException;
  public void onOnlySyncIntData(int propKey, int value) throws android.os.RemoteException;
  public void onOnlySyncFloatData(int propKey, float value) throws android.os.RemoteException;
}
