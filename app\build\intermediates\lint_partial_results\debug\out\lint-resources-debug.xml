http://schemas.android.com/apk/res-auto;;${\:app*debug*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*sourceProvider*0*resDir*0}/values-night/colors.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/button_background.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/play_pause_button_state.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/settings_icon.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/button_outline.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/text_stroke_background.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_camera_preview.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/item_video_record.xml,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*sourceProvider*0*resDir*0}/values/picker_styles.xml,${\:app*debug*sourceProvider*0*resDir*0}/values-night/picker_styles.xml,${\:app*debug*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*debug*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:colorPrimary,0,V"#3F51B5";colorPrimaryDark,0,V"#303F9F";dialog_label_text_color,0,V"#9C9C9C";dialog_label_text_color,1,V"#D8D8D8";background_light,0,V"#DEE2E5";background_light,1,V"#323232";background_primary_adaptive,0,V"#DEE2E5";background_primary_adaptive,1,V"#202020";button_text_unselected_adaptive,0,V"#808080";button_text_unselected_adaptive,1,V"#808080";text_secondary_adaptive,0,V"#666666";text_secondary_adaptive,1,V"#CCCCCC";button_text_unselected,0,V"@color/button_text_unselected_adaptive";button_text_unselected,1,V"@color/button_text_unselected_adaptive";colorAccent,0,V"#FF4081";background_secondary_adaptive,0,V"#DEE2E5";background_secondary_adaptive,1,V"#202020";white,0,V"#FFFFFF";button_background_selected,0,V"#44676767";button_background_selected,1,V"#44676767";container_background_adaptive,0,V"#F5F5F5";container_background_adaptive,1,V"#1A1A1A";number_picker_text_color,0,V"#000000";number_picker_text_color,1,V"#000000";text_adaptive,0,V"@color/text_primary_adaptive";text_adaptive,1,V"@color/text_primary_adaptive";separator_line_color,0,V"#9C9C9C";separator_line_color,1,V"#D8D8D8";text_primary_adaptive,0,V"#000000";text_primary_adaptive,1,V"#FFFFFF";player_control_text_color,0,V"#FFFFFF";player_control_text_color,1,V"#FFFFFF";button_outline_color,0,V"@color/colorPrimary";button_outline_color,1,V"#FFFFFF";status_bar_color,0,V"#DEE2E5";status_bar_color,1,V"#202020";button_text_selected_adaptive,0,V"#000000";button_text_selected_adaptive,1,V"#FFFFFF";dialog_background_color,0,V"#CED0D1";dialog_background_color,1,V"#858585";+drawable:button_background,2,F;play_pause_button_state,3,F;settings_icon,4,F;ic_launcher_foreground,5,F;ic_launcher_background,6,F;button_outline,7,F;text_stroke_background,8,F;+id:video_playback_page,9,F;video_player_container,9,F;left_camera_container,9,F;live_monitor_page,9,F;back_camera_container,9,F;left_camera_view,9,F;btn_playback_speed,9,F;btn_filter_back,9,F;camera_surface_view,10,F;tv_datetime_display,9,F;back_camera_view,9,F;tv_camera_direction,11,F;video_progress_bar,9,F;video_player_card,9,F;recycler_video_list,9,F;main_video_player,9,F;content_container,9,F;btn_sentinel_monitor,9,F;right_camera_container,9,F;btn_day_picker,9,F;front_camera_container,9,F;btn_video_playback,9,F;btn_filter_left,9,F;tv_current_time,9,F;player_controls_overlay,9,F;right_content_container,9,F;tv_record_time,11,F;btn_all_cameras,9,F;btn_filter_front,9,F;btn_filter_right,9,F;video_player_page,9,F;btn_time_filter_all,9,F;btn_hour_picker,9,F;btnStartStop,10,F;bottom_control_bar,9,F;btn_filter_all,9,F;front_camera_view,9,F;sentinel_monitor_page,9,F;right_preview_area,9,F;page_selector_container,9,F;btn_month_picker,9,F;btn_play_pause,9,F;left_control_panel,9,F;switch_sentry_auto,9,F;btn_settings,9,F;right_camera_view,9,F;+layout:activity_main,9,F;activity_camera_preview,10,F;item_video_record,11,F;+mipmap:ic_launcher_round,12,F;ic_launcher_round,13,F;ic_launcher_round,14,F;ic_launcher_round,15,F;ic_launcher_round,16,F;ic_launcher_round,17,F;ic_launcher,18,F;ic_launcher,19,F;ic_launcher,20,F;ic_launcher,21,F;ic_launcher,22,F;ic_launcher,23,F;+string:app_name,24,V"哨兵监控";+style:NumberPickerStyle,25,VNandroid\:textSize:18sp,android\:textColor:@color/number_picker_text_color,android\:background:@android\:color/transparent,android\:gravity:center,;NumberPickerStyle,26,VNandroid\:textSize:18sp,android\:textColor:@color/number_picker_text_color,android\:background:@android\:color/transparent,android\:gravity:center,;CustomButton,27,VDWidget.AppCompat.Button.Borderless,android\:textSize:14sp,android\:padding:0dp,android\:textColor:@color/colorPrimary,android\:drawableTint:@color/white,android\:minWidth:0dp,android\:minHeight:0dp,android\:includeFontPadding:false,;Theme.Sbjk,27,VDTheme.AppCompat.DayNight.NoActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,android\:windowFullscreen:false,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:navigationBarColor:@color/colorPrimaryDark,android\:statusBarColor:@color/background_light,android\:windowLightStatusBar:true,android\:windowBackground:@color/background_light,;Theme.Sbjk,28,VDTheme.AppCompat.DayNight.NoActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,android\:windowFullscreen:false,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:navigationBarColor:@color/background_light,android\:statusBarColor:@color/background_light,android\:windowBackground:@color/background_light,;+xml:data_extraction_rules,29,F;backup_rules,30,F;