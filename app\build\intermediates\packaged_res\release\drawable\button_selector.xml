<?xml version="1.0" encoding="utf-8"?>
<!-- 【重构】按钮状态选择器 - 支持主题适配的按钮背景 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态 -->
    <item android:state_selected="true" android:drawable="@drawable/button_background" />
    <!-- 按下状态 -->
    <item android:state_pressed="true" android:drawable="@drawable/button_outline_pressed" />
    <!-- 默认状态（未选中） -->
    <item android:drawable="@drawable/button_outline" />
</selector>