-- Merging decision tree log ---
manifest
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:2:1-159:12
INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:2:1-159:12
INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:2:1-159:12
INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:2:1-159:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31bbd90c8901d0911f3ed7bb95959bd7\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8c92ff17389098aaa14cda0c3c46136\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.camera:camera-video:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\11d551ef0fa918323253692ca44d9211\transformed\camera-video-1.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\0b222bb204d4b7bafba01f4e213ba6c3\transformed\camera-lifecycle-1.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e7135f5888e2c1687c4bd7cfa196189\transformed\camera-core-1.3.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1c49b378d3bfc9b2417728d67347e759\transformed\camera-view-1.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdec12638ca84b050de8b2364e0b87a2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0e0dd431addcd28d83cc92e48e3c0db\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\294d34fd5e703b3c4038dbd695d4b394\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0113e7ff3b852bf4e845dda4dcedacff\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc5059771eaba673976e07ed51be7264\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ccbeb6c0b11fe4c42deb1c6a9c2c6aa1\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2575c98a4b457a95178d26c5b890e96f\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8e2a0ec1549d5aeda9cc3ad873005b60\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\21a9b6a1a0fcec2a186269c7ed232b90\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5885c23aba5cb82796b9c0bf2ff51458\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d378a1fa9e7c53e4f0ab69352d3878e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a63954b77b113f3ab16034653559761e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac2b41cef31162c6dba39055a717dfc\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\48a6c63ff7628410f8aa369bbea972a0\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\b8bf7cf7fdee4a9af2e952a198394a23\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0c20f47d4e2e2609908bb01b31f9f330\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\95b33159c4a805f8e8cefb3dfd5431e0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\04f04863a9472a6e14c53097afa02c94\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b6a404ed77980ba612c57a299e5bd5f\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1585c4c2be2a8b3932e0e45525cfac7a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\decb13dd2ad18bd8f6487c30f06c05b4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c355f25d81538813abdab108d311b128\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e71f9b1e35057f0915d3c9a825c8c055\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd0f8b6329ce41c6293f61ae8c3e0d7f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad4f14a7a56b59156760a790f59a718d\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7554dde3be330bf02d20ce680e3f779\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9785e2caff9380ec13202caa3b9f1390\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1ed947031f74905bb1811f9e555e0ddc\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49bff969c455936e768bb390ca623b14\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03a5b86cc1fd402926454247ee78e7c6\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\64f0c95d7b551b1264271a9b934cbd8a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4eec405a8f7367258930c1137e1a0518\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69db0314eab67f037d92e4c7a2d0b87b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dbed63fe835b50be2d6d6e0d9c5a5918\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c1161375f9317274ba794e818ed8b11\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa69e74e6b71c4e2a047d349c1ab89fa\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1df4ad67b73e9f826f003702dbbd1ae1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:4:5-46
	android:versionName
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:6:5-42
		INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:3:5-51
	xmlns:android
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:2:11-69
	android:versionCode
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:5:5-28
		INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml
uses-sdk
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:8:5-10:40
INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:8:5-10:40
INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:8:5-10:40
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31bbd90c8901d0911f3ed7bb95959bd7\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31bbd90c8901d0911f3ed7bb95959bd7\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8c92ff17389098aaa14cda0c3c46136\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8c92ff17389098aaa14cda0c3c46136\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\11d551ef0fa918323253692ca44d9211\transformed\camera-video-1.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\11d551ef0fa918323253692ca44d9211\transformed\camera-video-1.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\0b222bb204d4b7bafba01f4e213ba6c3\transformed\camera-lifecycle-1.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\0b222bb204d4b7bafba01f4e213ba6c3\transformed\camera-lifecycle-1.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e7135f5888e2c1687c4bd7cfa196189\transformed\camera-core-1.3.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e7135f5888e2c1687c4bd7cfa196189\transformed\camera-core-1.3.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1c49b378d3bfc9b2417728d67347e759\transformed\camera-view-1.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1c49b378d3bfc9b2417728d67347e759\transformed\camera-view-1.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdec12638ca84b050de8b2364e0b87a2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdec12638ca84b050de8b2364e0b87a2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0e0dd431addcd28d83cc92e48e3c0db\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0e0dd431addcd28d83cc92e48e3c0db\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\294d34fd5e703b3c4038dbd695d4b394\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\294d34fd5e703b3c4038dbd695d4b394\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0113e7ff3b852bf4e845dda4dcedacff\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0113e7ff3b852bf4e845dda4dcedacff\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc5059771eaba673976e07ed51be7264\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc5059771eaba673976e07ed51be7264\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ccbeb6c0b11fe4c42deb1c6a9c2c6aa1\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ccbeb6c0b11fe4c42deb1c6a9c2c6aa1\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2575c98a4b457a95178d26c5b890e96f\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2575c98a4b457a95178d26c5b890e96f\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8e2a0ec1549d5aeda9cc3ad873005b60\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8e2a0ec1549d5aeda9cc3ad873005b60\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\21a9b6a1a0fcec2a186269c7ed232b90\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\21a9b6a1a0fcec2a186269c7ed232b90\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5885c23aba5cb82796b9c0bf2ff51458\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5885c23aba5cb82796b9c0bf2ff51458\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d378a1fa9e7c53e4f0ab69352d3878e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d378a1fa9e7c53e4f0ab69352d3878e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a63954b77b113f3ab16034653559761e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a63954b77b113f3ab16034653559761e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac2b41cef31162c6dba39055a717dfc\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac2b41cef31162c6dba39055a717dfc\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\48a6c63ff7628410f8aa369bbea972a0\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\48a6c63ff7628410f8aa369bbea972a0\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\b8bf7cf7fdee4a9af2e952a198394a23\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\b8bf7cf7fdee4a9af2e952a198394a23\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0c20f47d4e2e2609908bb01b31f9f330\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0c20f47d4e2e2609908bb01b31f9f330\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\95b33159c4a805f8e8cefb3dfd5431e0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\95b33159c4a805f8e8cefb3dfd5431e0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\04f04863a9472a6e14c53097afa02c94\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\04f04863a9472a6e14c53097afa02c94\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b6a404ed77980ba612c57a299e5bd5f\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b6a404ed77980ba612c57a299e5bd5f\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1585c4c2be2a8b3932e0e45525cfac7a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1585c4c2be2a8b3932e0e45525cfac7a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\decb13dd2ad18bd8f6487c30f06c05b4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\decb13dd2ad18bd8f6487c30f06c05b4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c355f25d81538813abdab108d311b128\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c355f25d81538813abdab108d311b128\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e71f9b1e35057f0915d3c9a825c8c055\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e71f9b1e35057f0915d3c9a825c8c055\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd0f8b6329ce41c6293f61ae8c3e0d7f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd0f8b6329ce41c6293f61ae8c3e0d7f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad4f14a7a56b59156760a790f59a718d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad4f14a7a56b59156760a790f59a718d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7554dde3be330bf02d20ce680e3f779\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7554dde3be330bf02d20ce680e3f779\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9785e2caff9380ec13202caa3b9f1390\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9785e2caff9380ec13202caa3b9f1390\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1ed947031f74905bb1811f9e555e0ddc\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1ed947031f74905bb1811f9e555e0ddc\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49bff969c455936e768bb390ca623b14\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49bff969c455936e768bb390ca623b14\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03a5b86cc1fd402926454247ee78e7c6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03a5b86cc1fd402926454247ee78e7c6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\64f0c95d7b551b1264271a9b934cbd8a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\64f0c95d7b551b1264271a9b934cbd8a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4eec405a8f7367258930c1137e1a0518\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4eec405a8f7367258930c1137e1a0518\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69db0314eab67f037d92e4c7a2d0b87b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69db0314eab67f037d92e4c7a2d0b87b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dbed63fe835b50be2d6d6e0d9c5a5918\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dbed63fe835b50be2d6d6e0d9c5a5918\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c1161375f9317274ba794e818ed8b11\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c1161375f9317274ba794e818ed8b11\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa69e74e6b71c4e2a047d349c1ab89fa\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa69e74e6b71c4e2a047d349c1ab89fa\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1df4ad67b73e9f826f003702dbbd1ae1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1df4ad67b73e9f826f003702dbbd1ae1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:10:9-38
		INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:9:9-35
		INJECTED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml
uses-permission#android.permission.CAMERA
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:13:5-65
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:13:22-62
uses-permission#android.permission.SYSTEM_CAMERA
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:14:5-72
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:14:22-69
uses-permission#android.permission.INTERACT_ACROSS_USERS
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:15:5-79
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:15:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:18:5-81
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:19:5-82
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:19:22-79
uses-permission#android.permission.RECORD_AUDIO
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:22:5-71
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:22:22-68
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:25:5-77
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:25:22-75
uses-permission#android.permission.START_ACTIVITIES_FROM_BACKGROUND
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:26:5-90
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:26:22-88
uses-permission#android.car.permission.CAR_POWER
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:27:5-71
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:27:22-69
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:28:5-80
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:28:22-78
uses-permission#android.permission.BIND_SERVICE
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:31:5-70
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:31:22-68
uses-permission#android.car.permission.CAR_INFO
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:32:5-70
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:32:22-68
uses-permission#android.car.permission.CAR_ENGINE_DETAILED
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:33:5-81
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:33:22-79
uses-permission#android.permission.INTERACT_ACROSS_USERS_FULL
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:36:5-84
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:36:22-82
uses-feature#0x20000
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:40:5-42:34
	android:glEsVersion
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:41:9-38
	android:required
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:42:9-32
uses-feature#android.hardware.camera
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:43:5-45:35
	android:required
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:45:9-33
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:44:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:46:5-48:35
	android:required
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:48:9-33
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:47:9-57
uses-feature#android.hardware.camera.front
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:49:5-51:35
	android:required
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:51:9-33
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:50:9-53
uses-feature#android.hardware.microphone
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:52:5-54:35
	android:required
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:54:9-33
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:53:9-51
queries
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:56:5-60:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:57:9-59:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:58:13-85
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:58:21-83
permission#com.autolink.sbjk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:62:5-64:46
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:64:9-44
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:63:9-82
uses-permission#com.autolink.sbjk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:65:5-97
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:65:22-95
application
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:67:5-157:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31bbd90c8901d0911f3ed7bb95959bd7\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31bbd90c8901d0911f3ed7bb95959bd7\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8c92ff17389098aaa14cda0c3c46136\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8c92ff17389098aaa14cda0c3c46136\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e7135f5888e2c1687c4bd7cfa196189\transformed\camera-core-1.3.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e7135f5888e2c1687c4bd7cfa196189\transformed\camera-core-1.3.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49bff969c455936e768bb390ca623b14\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49bff969c455936e768bb390ca623b14\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\64f0c95d7b551b1264271a9b934cbd8a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\64f0c95d7b551b1264271a9b934cbd8a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:77:9-42
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:74:9-35
	android:label
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:72:9-41
	android:hardwareAccelerated
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:79:9-43
	android:fullBackupContent
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:70:9-54
	android:roundIcon
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:73:9-54
	tools:targetApi
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:78:9-29
	android:persistent
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:76:9-34
	android:icon
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:71:9-43
	android:allowBackup
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:68:9-35
	android:theme
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:75:9-42
	android:dataExtractionRules
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:69:9-65
activity#com.autolink.sbjk.MainActivity
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:81:9-89:20
	android:exported
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:83:13-36
	android:configChanges
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:84:13-94
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:82:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:85:13-88:29
action#android.intent.action.MAIN
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:86:17-69
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:86:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:87:17-77
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:87:27-74
activity#com.autolink.dvr.p003ui.file.FileActivity
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:91:9-93:46
	android:launchMode
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:93:13-44
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:92:13-69
activity#com.autolink.dvr.p003ui.VideoActivity
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:95:9-97:46
	android:launchMode
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:97:13-44
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:96:13-65
activity#com.autolink.sbjk.CameraPreviewActivity
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:99:9-102:72
	android:screenOrientation
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:101:13-50
	android:theme
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:102:13-69
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:100:13-50
service#com.autolink.sbjk.common.service.AidlService
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:104:9-112:19
	android:enabled
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:106:13-35
	android:exported
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:107:13-36
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:105:13-55
intent-filter#action:name:com.autolink.sbjk.aidl.service+category:name:android.intent.category.DEFAULT
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:108:13-111:29
action#com.autolink.sbjk.aidl.service
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:109:17-72
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:109:25-70
category#android.intent.category.DEFAULT
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:110:17-75
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:110:27-73
service#com.autolink.sbjk.common.service.DVRService
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:114:9-115:56
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:115:13-54
receiver#com.autolink.sbjk.common.receiver.BootCompleteReceiver
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:117:9-124:20
	android:enabled
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:119:13-35
	android:exported
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:120:13-36
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:118:13-65
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:121:13-123:29
action#android.intent.action.BOOT_COMPLETED
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:122:17-78
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:122:25-76
uses-library#androidx.camera.extensions.impl
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:126:9-128:39
	android:required
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:128:13-37
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:127:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:130:9-138:19
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e7135f5888e2c1687c4bd7cfa196189\transformed\camera-core-1.3.2\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e7135f5888e2c1687c4bd7cfa196189\transformed\camera-core-1.3.2\AndroidManifest.xml:29:9-33:78
	android:enabled
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:132:13-36
	android:exported
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:133:13-37
	tools:ignore
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:134:13-42
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:131:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:135:13-137:88
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:30:13-32:89
MERGED from [androidx.camera:camera-camera2:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2602ef12df9abc64887a6d2459178ef5\transformed\camera-camera2-1.3.2\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:137:17-86
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:136:17-103
provider#androidx.startup.InitializationProvider
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:140:9-150:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\64f0c95d7b551b1264271a9b934cbd8a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\64f0c95d7b551b1264271a9b934cbd8a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	android:authorities
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:143:13-69
	android:exported
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:142:13-37
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:141:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:144:13-146:51
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4916646976a37bb24c722048d3d4a0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:146:17-49
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:145:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:147:13-149:51
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e2838d7b633d9ff7c22b67300331faa9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:149:17-49
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:148:17-78
service#com.autolink.sbjk.service.CameraService
ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:152:9-156:54
	android:enabled
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:154:13-35
	android:exported
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:155:13-37
	android:foregroundServiceType
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:156:13-51
	android:name
		ADDED from E:\XM\rsbjk-a\app\src\main\AndroidManifest.xml:153:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\69992366064d5c049095b4824cf18965\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f0b2045290d0a1e858a0c9aaddf52a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
