<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="18"
            column="36"
            startOffset="669"
            endLine="18"
            endColumn="77"
            endOffset="710"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement="">
            <range
                file="${:app*debug*sourceProvider*0*manifest*0}"
                startOffset="3094"
                endOffset="3114"/>
        </fix-replace>
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="78"
            column="9"
            startOffset="3094"
            endLine="78"
            endColumn="29"
            endOffset="3114"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-replace
            description="Delete tools:targetApi"
            replacement="">
            <range
                file="${:app*debug*sourceProvider*0*resDir*0}/values/themes.xml"
                startOffset="976"
                endOffset="995"/>
        </fix-replace>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/values/themes.xml"
            line="17"
            column="51"
            startOffset="976"
            endLine="17"
            endColumn="70"
            endOffset="995"/>
        <map>
            <condition minGE="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/common/base/BaseViewModel.java"
            line="40"
            column="66"
            startOffset="1276"
            endLine="40"
            endColumn="75"
            endOffset="1285"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.ConcurrentHashMap"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="newKeySet"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.ConcurrentHashMap#newKeySet`"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.CAMERA" requirement="android.permission.CAMERA"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/encoder/CameraYuvEncoder.java"
            line="544"
            column="13"
            startOffset="19255"
            endLine="569"
            endColumn="34"
            endOffset="20314"/>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="fffffffffe000000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/MainActivity.java"
            line="705"
            column="9"
            startOffset="23001"
            endLine="705"
            endColumn="31"
            endOffset="23023"/>
        <map>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="fffffffffe000000"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `android.content.ContextWrapper#startForegroundService`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/repository/PlaybackRepository.java"
            line="109"
            column="14"
            startOffset="2855"
            endLine="109"
            endColumn="24"
            endOffset="2865"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.CompletableFuture"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="thenAccept"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.CompletableFuture#thenAccept`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/repository/PlaybackRepository.java"
            line="124"
            column="14"
            startOffset="3369"
            endLine="124"
            endColumn="27"
            endOffset="3382"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.CompletableFuture"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="exceptionally"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.CompletableFuture#exceptionally`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/repository/PlaybackRepository.java"
            line="143"
            column="27"
            startOffset="3880"
            endLine="143"
            endColumn="38"
            endOffset="3891"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.CompletableFuture"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="supplyAsync"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.CompletableFuture#supplyAsync`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/repository/PlaybackRepository.java"
            line="144"
            column="14"
            startOffset="3957"
            endLine="144"
            endColumn="24"
            endOffset="3967"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.CompletableFuture"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="thenAccept"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.CompletableFuture#thenAccept`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/repository/PlaybackRepository.java"
            line="151"
            column="35"
            startOffset="4287"
            endLine="151"
            endColumn="39"
            endOffset="4291"/>
        <map>
            <entry
                name="owner"
                string="java.util.List"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="sort"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.List#sort`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/repository/PlaybackRepository.java"
            line="165"
            column="14"
            startOffset="4837"
            endLine="165"
            endColumn="27"
            endOffset="4850"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.CompletableFuture"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="exceptionally"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.CompletableFuture#exceptionally`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/repository/PlaybackRepository.java"
            line="405"
            column="31"
            startOffset="11515"
            endLine="405"
            endColumn="35"
            endOffset="11519"/>
        <map>
            <entry
                name="owner"
                string="java.util.List"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="sort"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.List#sort`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="58"
            column="34"
            startOffset="1628"
            endLine="58"
            endColumn="45"
            endOffset="1639"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.CompletableFuture"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="supplyAsync"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.CompletableFuture#supplyAsync`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="63"
            column="23"
            startOffset="1801"
            endLine="63"
            endColumn="27"
            endOffset="1805"/>
        <map>
            <entry
                name="owner"
                string="java.util.List"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="sort"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.List#sort`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="76"
            column="37"
            startOffset="2149"
            endLine="76"
            endColumn="40"
            endOffset="2152"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.CompletableFuture"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="get"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.CompletableFuture#get`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="156"
            column="20"
            startOffset="4589"
            endLine="156"
            endColumn="26"
            endOffset="4595"/>
        <map>
            <entry
                name="owner"
                string="java.util.Arrays"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Arrays#stream`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="157"
            column="18"
            startOffset="4620"
            endLine="157"
            endColumn="26"
            endOffset="4628"/>
        <map>
            <entry
                name="owner"
                string="java.util.stream.BaseStream"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="parallel"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.BaseStream#parallel`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="158"
            column="18"
            startOffset="4648"
            endLine="158"
            endColumn="21"
            endOffset="4651"/>
        <map>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#map`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="159"
            column="18"
            startOffset="4722"
            endLine="159"
            endColumn="24"
            endOffset="4728"/>
        <map>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="160"
            column="18"
            startOffset="4786"
            endLine="160"
            endColumn="32"
            endOffset="4800"/>
        <map>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="forEachOrdered"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#forEachOrdered`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/scanner/QuickVideoScanner.java"
            line="194"
            column="22"
            startOffset="5819"
            endLine="194"
            endColumn="26"
            endOffset="5823"/>
        <map>
            <entry
                name="owner"
                string="java.util.List"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="sort"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.List#sort`"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/service/CameraService.java"
            line="251"
            column="13"
            startOffset="7799"
            endLine="251"
            endColumn="58"
            endOffset="7844"/>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/autolink/sbjk/viewmodel/MainViewModel.java"
            line="54"
            column="69"
            startOffset="2059"
            endLine="54"
            endColumn="78"
            endOffset="2068"/>
        <map>
            <entry
                name="owner"
                string="java.util.concurrent.ConcurrentHashMap"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="newKeySet"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.concurrent.ConcurrentHashMap#newKeySet`"/>
        </map>
    </incident>

</incidents>
